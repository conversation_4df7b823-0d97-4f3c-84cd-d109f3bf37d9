"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var StorybookWorksService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookWorksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("./entities");
let StorybookWorksService = StorybookWorksService_1 = class StorybookWorksService {
    constructor(storybookRepository, folderRepository) {
        this.storybookRepository = storybookRepository;
        this.folderRepository = folderRepository;
        this.logger = new common_1.Logger(StorybookWorksService_1.name);
    }
    async createFolder(userId, createFolderDto) {
        const folder = this.folderRepository.create(Object.assign(Object.assign({}, createFolderDto), { userId }));
        return this.folderRepository.save(folder);
    }
    async getUserFolders(userId, queryFolderDto = {}) {
        const { page = 1, size = 10, keyword } = queryFolderDto;
        const queryBuilder = this.folderRepository.createQueryBuilder('folder')
            .where('folder.userId = :userId', { userId });
        if (keyword) {
            queryBuilder.andWhere('(folder.name LIKE :keyword OR folder.description LIKE :keyword)', { keyword: `%${keyword}%` });
        }
        const total = await queryBuilder.getCount();
        const items = await queryBuilder
            .orderBy('folder.order', 'ASC')
            .addOrderBy('folder.updatedAt', 'DESC')
            .skip((page - 1) * size)
            .take(size)
            .getMany();
        return { items, total };
    }
    async getFolderDetail(id, userId) {
        const folder = await this.folderRepository.findOne({
            where: { id }
        });
        if (!folder) {
            throw new common_1.NotFoundException('文件夹不存在');
        }
        if (folder.userId !== userId) {
            throw new common_1.BadRequestException('无权访问此文件夹');
        }
        return folder;
    }
    async updateFolder(id, userId, updateFolderDto) {
        const folder = await this.folderRepository.findOne({
            where: { id }
        });
        if (!folder) {
            throw new common_1.NotFoundException('文件夹不存在');
        }
        if (folder.userId !== userId) {
            throw new common_1.BadRequestException('无权修改此文件夹');
        }
        Object.assign(folder, updateFolderDto);
        return this.folderRepository.save(folder);
    }
    async deleteFolder(id, userId) {
        const folder = await this.folderRepository.findOne({
            where: { id }
        });
        if (!folder) {
            throw new common_1.NotFoundException('文件夹不存在');
        }
        if (folder.userId !== userId) {
            throw new common_1.BadRequestException('无权删除此文件夹');
        }
        if (folder.isSystem === 1) {
            throw new common_1.BadRequestException('系统文件夹不能删除');
        }
        await this.storybookRepository.update({ folderId: id, userId }, { folderId: null });
        await this.folderRepository.remove(folder);
    }
    async getUserStorybooks(userId, queryStorybookDto = {}) {
        const { page = 1, size = 10, keyword, status, folderId, isDeleted = 0, sortField = 'updatedAt', sortOrder = 'DESC', recentOnly = false } = queryStorybookDto;
        const queryBuilder = this.storybookRepository.createQueryBuilder('storybook')
            .where('storybook.userId = :userId', { userId })
            .andWhere('storybook.isDeleted = :isDeleted', { isDeleted });
        if (status !== undefined) {
            queryBuilder.andWhere('storybook.status = :status', { status });
        }
        if (folderId !== undefined) {
            if (folderId === 0) {
                queryBuilder.andWhere('storybook.folderId IS NULL');
            }
            else {
                queryBuilder.andWhere('storybook.folderId = :folderId', { folderId });
            }
        }
        if (keyword) {
            queryBuilder.andWhere('(storybook.title LIKE :keyword OR storybook.description LIKE :keyword)', { keyword: `%${keyword}%` });
        }
        if (recentOnly) {
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            queryBuilder.andWhere('storybook.lastEditedAt >= :sevenDaysAgo', { sevenDaysAgo });
        }
        const total = await queryBuilder.getCount();
        const items = await queryBuilder
            .orderBy(`storybook.${sortField}`, sortOrder)
            .skip((page - 1) * size)
            .take(size)
            .getMany();
        return { items, total };
    }
    async batchOperation(userId, batchOperationDto) {
        const { storybookIds, operation, targetFolderId } = batchOperationDto;
        if (storybookIds.length === 0) {
            throw new common_1.BadRequestException('作品ID列表不能为空');
        }
        const storybooks = await this.storybookRepository.find({
            where: {
                id: (0, typeorm_2.In)(storybookIds),
                userId
            }
        });
        if (storybooks.length !== storybookIds.length) {
            throw new common_1.BadRequestException('部分作品不存在或无权操作');
        }
        switch (operation) {
            case 'delete':
                await this.storybookRepository.update({ id: (0, typeorm_2.In)(storybookIds), userId }, {
                    isDeleted: 1,
                    deletedAt: new Date()
                });
                break;
            case 'restore':
                await this.storybookRepository.update({ id: (0, typeorm_2.In)(storybookIds), userId }, {
                    isDeleted: 0,
                    deletedAt: null
                });
                break;
            case 'move':
                if (targetFolderId !== undefined && targetFolderId !== 0) {
                    const targetFolder = await this.folderRepository.findOne({
                        where: { id: targetFolderId, userId }
                    });
                    if (!targetFolder) {
                        throw new common_1.BadRequestException('目标文件夹不存在');
                    }
                }
                await this.storybookRepository.update({ id: (0, typeorm_2.In)(storybookIds), userId }, {
                    folderId: targetFolderId === 0 ? null : targetFolderId
                });
                break;
            default:
                throw new common_1.BadRequestException('不支持的操作类型');
        }
    }
    async permanentlyDeleteStorybook(id, userId) {
        const storybook = await this.storybookRepository.findOne({
            where: { id, userId, isDeleted: 1 }
        });
        if (!storybook) {
            throw new common_1.NotFoundException('作品不存在或不在回收站中');
        }
        await this.storybookRepository.remove(storybook);
    }
    async emptyTrash(userId) {
        const storybooks = await this.storybookRepository.find({
            where: { userId, isDeleted: 1 }
        });
        if (storybooks.length > 0) {
            await this.storybookRepository.remove(storybooks);
        }
    }
    async createStorybook(userId, createStorybookDto) {
        this.logger.log(`开始创建绘本: userId=${userId}, title="${createStorybookDto.title}"`);
        if (createStorybookDto.status === undefined) {
            createStorybookDto.status = 0;
        }
        if (createStorybookDto.isPublic === undefined) {
            createStorybookDto.isPublic = 0;
        }
        if (createStorybookDto.source === undefined) {
            createStorybookDto.source = 'storybook';
        }
        const storybook = this.storybookRepository.create(Object.assign(Object.assign({}, createStorybookDto), { userId }));
        try {
            const savedStorybook = await this.storybookRepository.save(storybook);
            this.logger.log(`创建绘本成功: ID=${savedStorybook.id}, 标题="${savedStorybook.title}"`);
            return savedStorybook;
        }
        catch (error) {
            this.logger.error(`创建绘本失败: ${error.message}`, error.stack);
            throw new common_1.BadRequestException('创建绘本失败，请稍后重试');
        }
    }
    async getStorybookDetail(id, userId) {
        const storybook = await this.storybookRepository.findOne({
            where: { id }
        });
        if (!storybook) {
            throw new common_1.NotFoundException('绘本不存在');
        }
        if (storybook.userId !== userId && storybook.isPublic !== 1) {
            throw new common_1.BadRequestException('无权查看此绘本');
        }
        return storybook;
    }
    async updateStorybook(id, userId, updateStorybookDto) {
        const storybook = await this.storybookRepository.findOne({
            where: { id }
        });
        if (!storybook) {
            throw new common_1.NotFoundException('绘本不存在');
        }
        if (storybook.userId !== userId) {
            throw new common_1.BadRequestException('无权修改此绘本');
        }
        Object.assign(storybook, updateStorybookDto);
        storybook.lastEditedAt = new Date();
        try {
            const updatedStorybook = await this.storybookRepository.save(storybook);
            this.logger.log(`更新绘本成功: ID=${updatedStorybook.id}, 标题="${updatedStorybook.title}"`);
            return updatedStorybook;
        }
        catch (error) {
            this.logger.error(`更新绘本失败: ${error.message}`, error.stack);
            throw new common_1.BadRequestException('更新绘本失败，请稍后重试');
        }
    }
};
StorybookWorksService = StorybookWorksService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.StorybookEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.StorybookFolderEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], StorybookWorksService);
exports.StorybookWorksService = StorybookWorksService;
