"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MoveToFolderDto = exports.QueryFolderDto = exports.UpdateFolderDto = exports.CreateFolderDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateFolderDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件夹名称' }),
    (0, class_validator_1.IsNotEmpty)({ message: '文件夹名称不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFolderDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件夹描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFolderDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '封面图片URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFolderDto.prototype, "coverImg", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件夹颜色', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFolderDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件夹图标', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFolderDto.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateFolderDto.prototype, "order", void 0);
exports.CreateFolderDto = CreateFolderDto;
class UpdateFolderDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件夹名称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFolderDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件夹描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFolderDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '封面图片URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFolderDto.prototype, "coverImg", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件夹颜色', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFolderDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件夹图标', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFolderDto.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateFolderDto.prototype, "order", void 0);
exports.UpdateFolderDto = UpdateFolderDto;
class QueryFolderDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', required: false, default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryFolderDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', required: false, default: 10 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryFolderDto.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关键词搜索', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryFolderDto.prototype, "keyword", void 0);
exports.QueryFolderDto = QueryFolderDto;
class MoveToFolderDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '绘本ID数组' }),
    (0, class_validator_1.IsNotEmpty)({ message: '绘本ID数组不能为空' }),
    __metadata("design:type", Array)
], MoveToFolderDto.prototype, "storybookIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '目标文件夹ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], MoveToFolderDto.prototype, "folderId", void 0);
exports.MoveToFolderDto = MoveToFolderDto;
