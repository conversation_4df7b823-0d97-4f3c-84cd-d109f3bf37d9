"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookEntity = void 0;
const baseEntity_1 = require("../../../common/entity/baseEntity");
const typeorm_1 = require("typeorm");
const storybook_page_entity_1 = require("./storybook-page.entity");
const storybook_character_entity_1 = require("./storybook-character.entity");
const storybook_folder_entity_1 = require("./storybook-folder.entity");
let StorybookEntity = class StorybookEntity extends baseEntity_1.BaseEntity {
};
__decorate([
    (0, typeorm_1.Column)({ comment: '绘本标题' }),
    __metadata("design:type", String)
], StorybookEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '绘本描述', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '封面图片URL', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookEntity.prototype, "coverImg", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '创建用户ID' }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '状态(0:草稿,1:已发布,2:审核中,3:已拒绝)', default: 0 }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否公开(0:私有,1:公开)', default: 0 }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "isPublic", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否推荐(0:否,1:是)', default: 0 }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "isRecommended", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '浏览次数', default: 0 }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "viewCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '点赞次数', default: 0 }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "likeCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '绘本内容JSON', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookEntity.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '来源', nullable: true, default: 'storybook' }),
    __metadata("design:type", String)
], StorybookEntity.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '标签', type: 'simple-array', nullable: true }),
    __metadata("design:type", Array)
], StorybookEntity.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => storybook_page_entity_1.StorybookPageEntity, page => page.storybook),
    __metadata("design:type", Array)
], StorybookEntity.prototype, "pages", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => storybook_character_entity_1.StorybookCharacterEntity, character => character.storybook),
    __metadata("design:type", Array)
], StorybookEntity.prototype, "characters", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => storybook_folder_entity_1.StorybookFolderEntity, folder => folder.storybooks, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'folderId' }),
    __metadata("design:type", storybook_folder_entity_1.StorybookFolderEntity)
], StorybookEntity.prototype, "folder", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '所属文件夹ID', nullable: true }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "folderId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '页数', default: 0 }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "pageCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '字数', default: 0 }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "wordCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '最后编辑时间', nullable: true }),
    __metadata("design:type", Date)
], StorybookEntity.prototype, "lastEditedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否在回收站', default: 0 }),
    __metadata("design:type", Number)
], StorybookEntity.prototype, "isDeleted", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '删除时间', nullable: true }),
    __metadata("design:type", Date)
], StorybookEntity.prototype, "deletedAt", void 0);
StorybookEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'storybook' })
], StorybookEntity);
exports.StorybookEntity = StorybookEntity;
