"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookAdminController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const adminAuth_guard_1 = require("../../common/auth/adminAuth.guard");
const storybook_service_1 = require("./storybook.service");
let StorybookAdminController = class StorybookAdminController {
    constructor(storybookService) {
        this.storybookService = storybookService;
    }
    async getAllConfigs() {
        return this.storybookService.getAllConfigs();
    }
    async updateConfig(key, data) {
        return this.storybookService.setConfig(key, data.configVal, data.description);
    }
    async updateBatchConfig(data) {
        const results = [];
        console.log('开始批量更新配置:', Object.keys(data));
        try {
            for (const [key, value] of Object.entries(data)) {
                try {
                    const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
                    console.log(`正在更新配置: ${key} = ${stringValue}`);
                    let description;
                    if (key.startsWith('foxAssistant')) {
                        description = `小狐狸助手专用配置: ${key}`;
                    }
                    const result = await this.storybookService.setConfig(key, stringValue, description);
                    console.log(`配置更新成功: ${key}`);
                    results.push(result);
                }
                catch (error) {
                    console.error(`更新配置 ${key} 失败:`, error);
                }
            }
            console.log('批量更新配置完成，共更新', results.length, '项');
            const allConfigs = await this.storybookService.getAllConfigs();
            return {
                success: true,
                message: `成功更新 ${results.length} 项配置`,
                updatedKeys: Object.keys(data),
                configs: allConfigs
            };
        }
        catch (error) {
            console.error('批量更新配置失败:', error);
            throw new Error('批量更新配置失败: ' + error.message);
        }
    }
    async getAllPrompts() {
        return this.storybookService.getAllPrompts();
    }
    async createPrompt(data) {
        return this.storybookService.createPrompt(data);
    }
    async updatePrompt(id, data) {
        return this.storybookService.updatePrompt(id, data);
    }
    async deletePrompt(id) {
        return this.storybookService.deletePrompt(id);
    }
    async getAllTemplates() {
        return this.storybookService.getAllTemplates();
    }
    async createTemplate(data) {
        return this.storybookService.createTemplate(data);
    }
    async updateTemplate(id, data) {
        return this.storybookService.updateTemplate(id, data);
    }
    async deleteTemplate(id) {
        return this.storybookService.deleteTemplate(id);
    }
    async getStatistics(startDate, endDate) {
        return this.storybookService.getStatistics(startDate, endDate);
    }
    async getImages(page, limit, userId, imageType, auditStatus, storybookId, keyword, startDate, endDate) {
        return this.storybookService.getImages({
            page,
            limit,
            userId,
            imageType,
            auditStatus,
            storybookId,
            keyword,
            startDate,
            endDate
        });
    }
    async getImageDetail(id) {
        return this.storybookService.getImageDetail(id);
    }
    async auditImage(id, auditStatus, auditRemark) {
        return this.storybookService.auditImage(id, auditStatus, auditRemark);
    }
    async setImageQuality(id, qualityRating) {
        return this.storybookService.setImageQuality(id, qualityRating);
    }
    async deleteImage(id) {
        await this.storybookService.deleteImage(id);
        return { success: true, message: '删除成功' };
    }
    async getImageGenerationConfig() {
        return this.storybookService.getImageGenerationConfig();
    }
    async updateImageGenerationConfig(config) {
        return this.storybookService.updateImageGenerationConfig(config);
    }
    async getImageUsageStats(startDate, endDate) {
        return this.storybookService.getImageUsageStats(startDate, endDate);
    }
    async getContentSafetyConfig() {
        return this.storybookService.getContentSafetyConfig();
    }
    async updateContentSafetyConfig(config) {
        return this.storybookService.updateContentSafetyConfig(config);
    }
    async auditStorybookContent(id, req) {
        const userId = req.user['id'];
        return this.storybookService.auditStorybookContent(id, userId);
    }
    async autoAuditStorybook(id, req) {
        const userId = req.user['id'];
        return this.storybookService.autoAuditStorybook(id, userId);
    }
    async getViolationStats(startDate, endDate) {
        return this.storybookService.getViolationStats(startDate, endDate);
    }
};
__decorate([
    (0, common_1.Get)('config'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有配置' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getAllConfigs", null);
__decorate([
    (0, common_1.Put)('config/:key'),
    (0, swagger_1.ApiOperation)({ summary: '更新单个配置' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'key', description: '配置键' }),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "updateConfig", null);
__decorate([
    (0, common_1.Put)('config'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新配置' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "updateBatchConfig", null);
__decorate([
    (0, common_1.Get)('prompt'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有提示词' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getAllPrompts", null);
__decorate([
    (0, common_1.Post)('prompt'),
    (0, swagger_1.ApiOperation)({ summary: '创建提示词' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "createPrompt", null);
__decorate([
    (0, common_1.Put)('prompt/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新提示词' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '提示词ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "updatePrompt", null);
__decorate([
    (0, common_1.Delete)('prompt/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除提示词' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '提示词ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "deletePrompt", null);
__decorate([
    (0, common_1.Get)('template'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有模板' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getAllTemplates", null);
__decorate([
    (0, common_1.Post)('template'),
    (0, swagger_1.ApiOperation)({ summary: '创建模板' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "createTemplate", null);
__decorate([
    (0, common_1.Put)('template/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新模板' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '模板ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "updateTemplate", null);
__decorate([
    (0, common_1.Delete)('template/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除模板' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '模板ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "deleteTemplate", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: '获取统计数据' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: '开始日期' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: '结束日期' }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('image'),
    (0, swagger_1.ApiOperation)({ summary: '获取图像列表' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, description: '用户ID' }),
    (0, swagger_1.ApiQuery)({ name: 'imageType', required: false, description: '图像类型' }),
    (0, swagger_1.ApiQuery)({ name: 'auditStatus', required: false, description: '审核状态' }),
    (0, swagger_1.ApiQuery)({ name: 'storybookId', required: false, description: '绘本ID' }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', required: false, description: '关键词' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: '开始日期' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: '结束日期' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('userId')),
    __param(3, (0, common_1.Query)('imageType')),
    __param(4, (0, common_1.Query)('auditStatus')),
    __param(5, (0, common_1.Query)('storybookId')),
    __param(6, (0, common_1.Query)('keyword')),
    __param(7, (0, common_1.Query)('startDate')),
    __param(8, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number, Number, Number, Number, String, Date,
        Date]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getImages", null);
__decorate([
    (0, common_1.Get)('image/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取图像详情' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '图像ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getImageDetail", null);
__decorate([
    (0, common_1.Put)('image/:id/audit'),
    (0, swagger_1.ApiOperation)({ summary: '审核图像' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '图像ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)('auditStatus')),
    __param(2, (0, common_1.Body)('auditRemark')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "auditImage", null);
__decorate([
    (0, common_1.Put)('image/:id/quality'),
    (0, swagger_1.ApiOperation)({ summary: '设置图像质量评级' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '图像ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)('qualityRating')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "setImageQuality", null);
__decorate([
    (0, common_1.Delete)('image/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除图像' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '图像ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "deleteImage", null);
__decorate([
    (0, common_1.Get)('image-config'),
    (0, swagger_1.ApiOperation)({ summary: '获取图像生成配置' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getImageGenerationConfig", null);
__decorate([
    (0, common_1.Put)('image-config'),
    (0, swagger_1.ApiOperation)({ summary: '更新图像生成配置' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "updateImageGenerationConfig", null);
__decorate([
    (0, common_1.Get)('image-stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取图像资源使用统计' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: '开始日期' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: '结束日期' }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getImageUsageStats", null);
__decorate([
    (0, common_1.Get)('content-safety/config'),
    (0, swagger_1.ApiOperation)({ summary: '获取内容安全配置' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getContentSafetyConfig", null);
__decorate([
    (0, common_1.Put)('content-safety/config'),
    (0, swagger_1.ApiOperation)({ summary: '更新内容安全配置' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "updateContentSafetyConfig", null);
__decorate([
    (0, common_1.Post)(':id/audit-content'),
    (0, swagger_1.ApiOperation)({ summary: '审核绘本内容' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '绘本ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "auditStorybookContent", null);
__decorate([
    (0, common_1.Post)(':id/auto-audit'),
    (0, swagger_1.ApiOperation)({ summary: '自动审核绘本' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '绘本ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "autoAuditStorybook", null);
__decorate([
    (0, common_1.Get)('violation-stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取违规内容统计' }),
    (0, common_1.UseGuards)(adminAuth_guard_1.AdminAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: '开始日期' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: '结束日期' }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], StorybookAdminController.prototype, "getViolationStats", null);
StorybookAdminController = __decorate([
    (0, swagger_1.ApiTags)('Admin Storybook'),
    (0, common_1.Controller)('admin/storybook'),
    __metadata("design:paramtypes", [storybook_service_1.StorybookService])
], StorybookAdminController);
exports.StorybookAdminController = StorybookAdminController;
