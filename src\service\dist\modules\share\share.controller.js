"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwtAuth_guard_1 = require("../../common/auth/jwtAuth.guard");
const create_share_dto_1 = require("./dto/create-share.dto");
const delete_share_dto_1 = require("./dto/delete-share.dto");
const get_share_content_dto_1 = require("./dto/get-share-content.dto");
const get_shared_html_dto_1 = require("./dto/get-shared-html.dto");
const get_user_shares_dto_1 = require("./dto/get-user-shares.dto");
const share_service_1 = require("./share.service");
let ShareController = class ShareController {
    constructor(shareService) {
        this.shareService = shareService;
    }
    async createHtmlShare(createShareDto, req) {
        const userId = req.user && typeof req.user === 'object' && 'id' in req.user ? req.user.id : null;
        console.log('[ShareController] 创建HTML分享请求');
        console.log('[ShareController] 用户ID:', userId);
        console.log('[ShareController] 请求用户对象:', req.user);
        console.log('[ShareController] 请求体类型:', typeof createShareDto);
        if (createShareDto.htmlContent) {
            console.log('[ShareController] htmlContent 类型:', typeof createShareDto.htmlContent);
            console.log('[ShareController] htmlContent 长度:', createShareDto.htmlContent.length);
            console.log('[ShareController] htmlContent 预览:', createShareDto.htmlContent.substring(0, 100) + '...');
        }
        else {
            console.warn('[ShareController] 请求中缺少 htmlContent');
        }
        try {
            const shareCode = await this.shareService.createHtmlShare(createShareDto, userId);
            console.log('[ShareController] 生成分享代码:', shareCode);
            const responseData = { shareCode };
            console.log('[ShareController] 返回的数据:', responseData);
            return { data: responseData };
        }
        catch (error) {
            console.error('[ShareController] 创建HTML分享失败:', error);
            throw error;
        }
    }
    async getSharedHtml(getSharedHtmlDto, req) {
        console.log('[ShareController] 获取分享的HTML内容请求');
        console.log('[ShareController] 分享代码:', getSharedHtmlDto.shareCode);
        console.log('[ShareController] 请求头:', req.headers);
        try {
            const htmlContent = await this.shareService.getSharedHtml(getSharedHtmlDto, req);
            console.log('[ShareController] 获取分享内容成功');
            console.log('[ShareController] 返回的HTML内容长度:', htmlContent.htmlContent.length);
            return { success: true, data: htmlContent };
        }
        catch (error) {
            console.error('[ShareController] 获取分享内容失败:', error);
            throw error;
        }
    }
    async getUserShares(getUserSharesDto, req) {
        console.log('[ShareController] 获取用户分享列表请求');
        const userId = req.user && typeof req.user === 'object' && 'id' in req.user ? req.user.id : null;
        if (!userId) {
            console.error('[ShareController] 用户未登录或ID无效');
            throw new Error('用户未登录或ID无效');
        }
        console.log('[ShareController] 用户ID:', userId);
        console.log('[ShareController] 查询参数:', getUserSharesDto);
        try {
            const result = await this.shareService.getUserShares(userId, getUserSharesDto);
            console.log('[ShareController] 获取用户分享列表成功, 数量:', result.count);
            return { success: true, data: result };
        }
        catch (error) {
            console.error('[ShareController] 获取用户分享列表失败:', error);
            throw error;
        }
    }
    async deleteShare(deleteShareDto, req) {
        console.log('[ShareController] 删除分享请求');
        const userId = req.user && typeof req.user === 'object' && 'id' in req.user ? req.user.id : null;
        if (!userId) {
            console.error('[ShareController] 用户未登录或ID无效');
            throw new Error('用户未登录或ID无效');
        }
        console.log('[ShareController] 用户ID:', userId);
        console.log('[ShareController] 分享ID:', deleteShareDto.shareId);
        try {
            const result = await this.shareService.deleteShare(userId, deleteShareDto.shareId);
            console.log('[ShareController] 删除分享成功');
            return { success: true, message: '删除分享成功' };
        }
        catch (error) {
            console.error('[ShareController] 删除分享失败:', error);
            throw error;
        }
    }
    async getShareContent(getShareContentDto, req) {
        console.log('[ShareController] 获取分享内容请求');
        const userId = req.user && typeof req.user === 'object' && 'id' in req.user ? req.user.id : null;
        if (!userId) {
            console.error('[ShareController] 用户未登录或ID无效');
            throw new Error('用户未登录或ID无效');
        }
        console.log('[ShareController] 用户ID:', userId);
        console.log('[ShareController] 分享ID:', getShareContentDto.shareId);
        try {
            const result = await this.shareService.getShareContent(userId, getShareContentDto.shareId);
            console.log('[ShareController] 获取分享内容成功');
            return { success: true, data: result };
        }
        catch (error) {
            console.error('[ShareController] 获取分享内容失败:', error);
            throw error;
        }
    }
};
__decorate([
    (0, common_1.Post)('createHtmlShare'),
    (0, swagger_1.ApiOperation)({ summary: '创建HTML分享' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_share_dto_1.CreateShareDto, Object]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "createHtmlShare", null);
__decorate([
    (0, common_1.Get)('getSharedHtml'),
    (0, swagger_1.ApiOperation)({ summary: '获取分享的HTML内容' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_shared_html_dto_1.GetSharedHtmlDto, Object]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "getSharedHtml", null);
__decorate([
    (0, common_1.Get)('getUserShares'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户的HTML分享列表' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_user_shares_dto_1.GetUserSharesDto, Object]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "getUserShares", null);
__decorate([
    (0, common_1.Post)('deleteShare'),
    (0, swagger_1.ApiOperation)({ summary: '删除HTML分享' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [delete_share_dto_1.DeleteShareDto, Object]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "deleteShare", null);
__decorate([
    (0, common_1.Get)('getShareContent'),
    (0, swagger_1.ApiOperation)({ summary: '获取分享内容' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_share_content_dto_1.GetShareContentDto, Object]),
    __metadata("design:returntype", Promise)
], ShareController.prototype, "getShareContent", null);
ShareController = __decorate([
    (0, swagger_1.ApiTags)('share'),
    (0, common_1.Controller)('share'),
    __metadata("design:paramtypes", [share_service_1.ShareService])
], ShareController);
exports.ShareController = ShareController;
