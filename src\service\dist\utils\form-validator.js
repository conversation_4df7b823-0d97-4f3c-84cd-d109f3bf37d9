"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormValidator = void 0;
class FormValidator {
    static validateFormFieldsJson(fieldsJson) {
        try {
            const fields = JSON.parse(fieldsJson);
            if (!Array.isArray(fields)) {
                return false;
            }
            for (const field of fields) {
                if (!field.type || !field.name || !field.label) {
                    return false;
                }
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
    static validateFieldName(name) {
        const nameRegex = /^[a-zA-Z][a-zA-Z0-9_]*$/;
        return nameRegex.test(name);
    }
    static validateFieldType(type) {
        const supportedTypes = [
            'input',
            'textarea',
            'select',
            'radio',
            'checkbox',
            'switch',
            'slider',
            'date',
            'time',
            'datetime',
            'rate',
            'color',
            'number'
        ];
        return supportedTypes.includes(type);
    }
    static validateFieldOptions(options) {
        if (!Array.isArray(options) || options.length === 0) {
            return false;
        }
        for (const option of options) {
            if (typeof option === 'object' && (!option.label || !option.value)) {
                return false;
            }
            else if (typeof option !== 'object' && typeof option !== 'string') {
                return false;
            }
        }
        return true;
    }
    static validateField(field) {
        if (!field.type || !field.name || !field.label) {
            return false;
        }
        if (!this.validateFieldName(field.name)) {
            return false;
        }
        if (!this.validateFieldType(field.type)) {
            return false;
        }
        if (['select', 'radio', 'checkbox'].includes(field.type) && field.options) {
            if (!this.validateFieldOptions(field.options)) {
                return false;
            }
        }
        if (['number', 'slider'].includes(field.type)) {
            if (field.min !== undefined && field.max !== undefined && field.min > field.max) {
                return false;
            }
        }
        return true;
    }
    static validateFields(fields) {
        if (!Array.isArray(fields)) {
            return false;
        }
        for (const field of fields) {
            if (!this.validateField(field)) {
                return false;
            }
        }
        const fieldNames = fields.map(field => field.name);
        const uniqueFieldNames = new Set(fieldNames);
        if (fieldNames.length !== uniqueFieldNames.size) {
            return false;
        }
        return true;
    }
}
exports.FormValidator = FormValidator;
