<template>
  <div class="centered-input-container flex items-center justify-center min-h-screen relative overflow-hidden">
    <!-- 美化的背景装饰 -->
    <div class="absolute inset-0 pointer-events-none">
      <!-- 主背景渐变 -->
      <div class="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-indigo-50/20 to-purple-50/10 dark:from-blue-900/20 dark:via-indigo-900/15 dark:to-purple-900/10"></div>

      <!-- 浮动装饰元素 -->
      <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-indigo-500/5 rounded-full blur-2xl animate-pulse"></div>
      <div class="absolute bottom-1/3 right-1/4 w-24 h-24 bg-gradient-to-br from-purple-400/10 to-pink-500/5 rounded-full blur-xl"></div>
      <div class="absolute top-1/2 right-1/3 w-16 h-16 bg-gradient-to-br from-green-400/10 to-teal-500/5 rounded-full blur-lg"></div>
    </div>

    <div class="centered-input-wrapper w-full max-w-4xl mx-auto px-6 relative z-10">
      <!-- 增强的AI教学助手标题区域 -->
      <div class="text-center mb-12 animate__animated animate__fadeInUp">
        <!-- 美化的图标容器 -->
        <div class="mb-8">
          <div class="w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-blue-500/25 dark:shadow-blue-600/30 transform transition-all duration-500 hover:scale-110 hover:rotate-3 ai-float">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
        </div>

        <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-800 via-blue-600 to-indigo-700 dark:from-gray-100 dark:via-blue-400 dark:to-indigo-300 bg-clip-text text-transparent mb-4 leading-tight">
          下午好，V：数字化新学习
        </h1>

        <p class="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto">
          欢迎使用AI教学助手！我是您的智能教学伙伴，随时为您提供
          <span class="font-semibold text-blue-600 dark:text-blue-400">专业的教学支持</span>
          和创新的教学方案。
        </p>

        <!-- 快速提示标签 -->
        <div class="flex flex-wrap justify-center gap-3 mb-8">
          <span class="px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium border border-blue-200 dark:border-blue-700/50 hover:bg-blue-200 dark:hover:bg-blue-800/40 transition-colors cursor-default">
            💡 教学咨询
          </span>
          <span class="px-4 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-sm font-medium border border-green-200 dark:border-green-700/50 hover:bg-green-200 dark:hover:bg-green-800/40 transition-colors cursor-default">
            📚 课程设计
          </span>
          <span class="px-4 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-sm font-medium border border-purple-200 dark:border-purple-700/50 hover:bg-purple-200 dark:hover:bg-purple-800/40 transition-colors cursor-default">
            🎯 教学方法
          </span>
        </div>
      </div>

      <!-- 插件搜索建议列表 -->
      <div
        v-if="showSuggestions && !isSelectedApp && !usingPlugin"
        class="flex mb-2 w-full px-2 py-1 justify-center items-center flex-col m-auto rounded-lg shadow-sm ring-1 ring-gray-300 resize-none dark:ring-gray-750 dark:bg-gray-800"
        :class="[isMobile ? 'mx-2' : 'mx-10']"
        :style="{ maxWidth: '64rem', minHeight: '1.5rem' }"
      >
        <div
          v-if="searchResults.length === 0"
          class="flex items-center py-2 px-2 rounded-lg w-full cursor-pointer transition duration-150 ease-in-out"
        >
          <p class="text-sm text-gray-500 dark:text-gray-400 flex-grow truncate">
            使用 @ 搜索应用程序
          </p>
        </div>
        <div
          v-for="app in searchResults"
          :key="app.id"
          @click="selectApp(app)"
          class="flex items-center bg-white dark:bg-gray-800 hover:bg-gray-100 py-2 px-2 dark:hover:bg-gray-700 rounded-lg w-full cursor-pointer transition duration-150 ease-in-out"
        >
          <div
            class="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center overflow-hidden shadow-sm border border-gray-300 mr-3"
          >
            <span
              v-if="app.coverImg && app.coverImg.startsWith('emoji:')"
              class="w-8 h-8 text-xl flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-full"
            >
              {{ app.coverImg.replace('emoji:', '') }}
            </span>
            <img
              v-else-if="app.coverImg"
              :src="app.coverImg"
              alt="Cover Image"
              class="w-8 h-8 rounded-full flex justify-start"
            />
            <span
              v-else
              class="w-8 h-8 text-sm font-medium text-gray-700 dark:text-gray-400 rounded-full flex items-center justify-center dark:bg-gray-700"
            >
              {{ app.name.charAt(0) }}
            </span>
          </div>
          <h3
            class="text-md font-bold text-gray-600 dark:text-primary-500 mr-3 flex-shrink-0"
          >
            {{ app.name }}
          </h3>
          <p class="text-sm text-gray-400 dark:text-gray-400 flex-grow truncate">
            {{ app.des }}
          </p>
        </div>
      </div>

      <!-- 增强的输入框区域 -->
      <div class="input-section">
        <!-- 美化的主输入框容器 -->
        <div class="main-input-container relative bg-white/95 dark:bg-gray-800/95 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-white/50 dark:border-gray-700/50 group overflow-hidden">
          <!-- 输入框装饰背景 -->
          <div class="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20 dark:from-blue-900/20 dark:via-transparent dark:to-indigo-900/10 pointer-events-none"></div>

          <!-- 顶部装饰光线 -->
          <div class="absolute top-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-blue-400/40 to-transparent dark:via-blue-300/50"></div>

          <!-- 已选择插件/应用显示区域 -->
          <div
            v-if="isSelectedApp || usingPlugin"
            class="self-start w-full bg-opacity dark:bg-gray-700 rounded-t-xl"
          >
            <template v-if="usingPlugin && !isSelectedApp" class="w-full">
              <!-- 显示当前使用的插件 -->
              <div
                class="relative px-2 flex items-center justify-start bg-opacity rounded-t-lg h-12 text-gray-700 dark:text-gray-400 w-full dark:bg-gray-700 group"
              >
                <div
                  class="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center overflow-hidden shadow-sm border border-gray-300 mr-3"
                >
                  <span
                    v-if="usingPlugin.pluginImg && usingPlugin.pluginImg.startsWith('emoji:')"
                    class="w-8 h-8 text-xl flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-full"
                  >
                    {{ usingPlugin.pluginImg.replace('emoji:', '') }}
                  </span>
                  <img
                    v-else-if="usingPlugin.pluginImg"
                    :src="usingPlugin.pluginImg"
                    alt="Cover Image"
                    class="w-8 h-8 rounded-full flex justify-start"
                  />
                  <span
                    v-else
                    class="w-8 h-8 text-sm font-medium text-gray-700 dark:text-gray-400 rounded-full flex items-center justify-center dark:bg-gray-700"
                  >
                    {{ usingPlugin.pluginName.charAt(0) }}
                  </span>
                </div>

                <h3
                  class="text-md font-bold text-gray-700 dark:text-gray-400 mr-3 flex-shrink-0 flex justify-start"
                >
                  {{ usingPlugin.pluginName }}
                </h3>
                <p
                  class="text-sm text-gray-400 dark:text-gray-400 truncate pr-10"
                >
                  {{ usingPlugin.description }}
                </p>

                <div
                  class="absolute top-1/2 right-2 transform -translate-y-1/2 cursor-pointer text-gray-300 group-hover:text-gray-500"
                  @click="clearSelectApp()"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
            </template>
            <template v-if="isSelectedApp && !usingPlugin" class="w-full">
              <!-- 显示选中的应用 -->
              <div
                class="relative px-2 flex items-center justify-start bg-opacity rounded-t-lg h-12 text-gray-700 dark:text-gray-400 w-full dark:bg-gray-700 group"
              >
                <div
                  class="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center overflow-hidden shadow-sm border border-gray-300 mr-3"
                >
                  <span
                    v-if="selectedApp.coverImg && selectedApp.coverImg.startsWith('emoji:')"
                    class="w-8 h-8 text-xl flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-full"
                  >
                    {{ selectedApp.coverImg.replace('emoji:', '') }}
                  </span>
                  <img
                    v-else-if="selectedApp.coverImg"
                    :src="selectedApp.coverImg"
                    alt="Cover Image"
                    class="w-8 h-8 rounded-full flex justify-start"
                  />
                  <span
                    v-else
                    class="w-8 h-8 text-sm font-medium text-gray-700 dark:text-gray-400 rounded-full flex items-center justify-center dark:bg-gray-700"
                  >
                    {{ selectedApp.name.charAt(0) }}
                  </span>
                </div>
                <h3
                  class="text-md font-bold text-gray-600 dark:text-gray-400 mr-3 flex-shrink-0 flex justify-start"
                >
                  {{ selectedApp.name }}
                </h3>
                <p
                  class="text-sm text-gray-400 dark:text-gray-400 truncate pr-10"
                >
                  {{ selectedApp.des }}
                </p>
                <div
                  class="absolute top-1/2 right-2 transform -translate-y-1/2 cursor-pointer text-gray-300 group-hover:text-gray-500"
                  @click="clearSelectApp()"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
            </template>
          </div>

          <!-- 美化的输入框主体 -->
          <div class="flex items-center p-4 gap-3 relative z-10">
            <!-- 增强的输入框 -->
            <div class="flex-1">
              <textarea
                ref="inputRef"
                v-model="prompt"
                :placeholder="placeholder"
                class="w-full border-0 bg-transparent text-gray-800 dark:text-gray-200 placeholder:text-gray-400 dark:placeholder:text-gray-500 resize-none focus:outline-none text-lg leading-7 py-3 font-medium transition-all duration-200"
                :style="{ maxHeight: '150px', minHeight: '50px' }"
                @input="handleInput"
                @keydown="handleKeydown"
                @paste="handlePaste"
              ></textarea>
            </div>

            <!-- 美化的右侧工具栏 -->
            <div class="flex items-center gap-2">
              <!-- 美化的文件上传按钮 -->
              <button
                v-if="showFileUpload && !isUploading"
                @click="triggerFileUpload"
                class="p-3 text-gray-400 hover:text-blue-500 dark:text-gray-500 dark:hover:text-blue-400 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-all duration-300 hover:scale-110 active:scale-95 group"
                title="上传文件"
              >
                <svg class="w-5 h-5 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
              </button>

              <!-- 美化的语音按钮 -->
              <button
                class="p-3 text-gray-400 hover:text-green-500 dark:text-gray-500 dark:hover:text-green-400 rounded-xl hover:bg-green-50 dark:hover:bg-green-900/30 transition-all duration-300 hover:scale-110 active:scale-95 group"
                title="语音输入"
              >
                <svg class="w-5 h-5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </button>

              <!-- 美化的发送按钮 -->
              <button
                v-if="!isStreamIn"
                @click="handleSubmit"
                :disabled="buttonDisabled"
                class="p-3 rounded-xl transition-all duration-300 transform hover:scale-110 active:scale-95 shadow-lg"
                :class="{
                  'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-blue-500/25 hover:shadow-blue-500/40': !buttonDisabled,
                  'bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed shadow-gray-300/25': buttonDisabled
                }"
                title="发送消息"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>

              <!-- 美化的停止按钮 -->
              <button
                v-if="isStreamIn"
                @click="handleStop"
                class="p-3 rounded-xl bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white transition-all duration-300 transform hover:scale-110 active:scale-95 shadow-lg shadow-red-500/25 hover:shadow-red-500/40"
                title="停止生成"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <rect x="6" y="6" width="12" height="12" rx="2" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 美化的快速提示按钮 -->
        <div class="flex flex-wrap justify-center gap-3 mt-6 px-4">
          <button
            v-for="(suggestion, index) in quickSuggestions"
            :key="index"
            @click="useQuickSuggestion(suggestion)"
            class="px-4 py-2.5 text-sm font-medium bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 dark:from-gray-700 dark:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-500 text-blue-700 dark:text-blue-300 rounded-full border border-blue-200/60 dark:border-gray-600/60 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-blue-600/30 transform hover:scale-105 active:scale-95 hover:-translate-y-0.5"
          >
            {{ suggestion }}
          </button>
        </div>

        <!-- 底部提示信息 -->
        <div class="text-center mt-6 px-4">
          <p class="text-sm text-gray-500 dark:text-gray-400 leading-relaxed">
            💡 提示：您可以使用
            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono border border-gray-300 dark:border-gray-600">Enter</kbd>
            发送消息，或
            <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono border border-gray-300 dark:border-gray-600">Shift + Enter</kbd>
            换行
          </p>
        </div>
      </div>

      <!-- 隐藏的文件上传输入 -->
      <input
        ref="fileInput"
        type="file"
        class="hidden"
        @change="handleFileSelect"
        accept="image/*, .pdf, .txt, .docx, .pptx, .xlsx, .xml, .js, .xslt, .json, .sql"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, inject, watch } from 'vue';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useChatStore, useAuthStore } from '@/store';
import { fetchSearchAppsAPI, fetchQueryOneCatAPI } from '@/api/appStore';

// 基础设置
const { isMobile } = useBasicLayout();
const chatStore = useChatStore();
const authStore = useAuthStore();

// 输入相关refs
const inputRef = ref<HTMLTextAreaElement>();
const fileInput = ref<HTMLInputElement>();

// 状态管理
const isUploading = ref(false);
const prompt = computed({
  get: () => chatStore.prompt || '',
  set: (value) => chatStore.setPrompt(value),
});

// 插件相关状态
const searchResults = ref<any[]>([]);
const showSuggestions = ref(false);
const selectedApp = ref();
const isSelectedApp = ref(false);
let searchTimeout: string | number | NodeJS.Timeout | null | undefined = null;

// 注入的函数
const onConversation = inject<any>('onConversation');
const createNewChatGroup = inject<() => Promise<void>>('createNewChatGroup', () => Promise.resolve());

// 计算属性
const isStreamIn = computed(() => chatStore.isStreamIn !== undefined ? chatStore.isStreamIn : false);
const usingPlugin = computed(() => chatStore.currentPlugin);
const buttonDisabled = computed(() => !prompt.value?.trim() || isStreamIn.value);
const showFileUpload = computed(() => {
  // 根据需要配置文件上传的显示条件
  return true;
});

const placeholder = computed(() => {
  return '有什么可以帮您的吗？';
});

// 快速提示选项
const quickSuggestions = ref([
  '📚 课堂管理',
  '🎯 教学设计',
  '🤖 AI 助手',
  '📝 教案',
  '🎨 数字化',
  '📊 数据分析',
  '🔍 AI 应用',
  '📈 云平台'
]);

// 计算当前活动组信息
const activeGroupInfo = computed(() => chatStore.getChatByGroupInfo());
const configObj = computed(() => {
  const configString = activeGroupInfo.value?.config;
  if (!configString) {
    return {}; // 提早返回一个空对象
  }

  try {
    return JSON.parse(configString);
  } catch (e) {
    return {}; // 解析失败时返回一个空对象
  }
});

const activeModelName = computed(() =>
  String(configObj?.value.modelInfo.modelName)
);
const activeModelKeyType = computed(() =>
  Number(configObj?.value.modelInfo.keyType)
);

const activeModelAvatar = computed(() => {
  return String(
    usingPlugin?.value?.pluginImg ||
      configObj?.value.modelInfo?.modelAvatar ||
      ''
  );
});

// 自动调整高度
const autoResize = () => {
  if (inputRef.value) {
    const textarea = inputRef.value;
    textarea.style.height = '24px'; // 重置高度
    const scrollHeight = textarea.scrollHeight;
    const maxHeight = 200; // 最大高度
    textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';

    if (scrollHeight > maxHeight) {
      textarea.style.overflowY = 'auto';
    } else {
      textarea.style.overflowY = 'hidden';
    }
  }
};

// 处理输入事件，包括@符号搜索
const handleInput = async (event: Event) => {
  autoResize(); // 先调整高度

  const inputElement = event.target as HTMLTextAreaElement;
  const inputValue = inputElement.value;
  showSuggestions.value = inputValue.startsWith('@');

  // 清除之前的定时器，如果有的话
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  if (showSuggestions.value && !isSelectedApp.value) {
    const searchTerm = inputValue.slice(1); // 去掉'@'
    // 使用定时器来节流搜索请求
    searchTimeout = setTimeout(async () => {
      if (searchTerm.length > 0) {
        try {
          const results: any = await fetchSearchAppsAPI({
            keyword: searchTerm,
          });
          searchResults.value = results.data.rows;
        } catch (error) {
          console.error('Error fetching search results:', error);
          searchResults.value = [];
        }
      } else {
        searchResults.value = [];
      }
    }, 1000); // 设置1秒的延迟
  } else {
    searchResults.value = [];
  }
};

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (!isMobile.value) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit();
    }
  } else {
    if (event.key === 'Enter' && event.ctrlKey) {
      event.preventDefault();
      handleSubmit();
    }
  }
};

// 处理粘贴
const handlePaste = (event: ClipboardEvent) => {
  // 处理粘贴逻辑，可以参考Footer组件的实现
  nextTick(() => {
    autoResize();
  });
};

// 选择应用程序
const selectApp = async (app: any) => {
  // 这里可以设置选中的应用程序的逻辑
  selectedApp.value = app;
  isSelectedApp.value = true;
  await chatStore.setPrompt('');
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus();
    }
  });
};

// 清除应用程序选择
const clearSelectApp = async () => {
  searchResults.value = [];
  showSuggestions.value = false;
  isSelectedApp.value = false;
  selectedApp.value = null;
  chatStore.setUsingPlugin(null);
};

// 使用快速提示
const useQuickSuggestion = async (suggestion: string) => {
  await chatStore.setPrompt(suggestion);
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus();
      autoResize();
    }
  });
};

// 提交消息
const handleSubmit = async () => {
  if (buttonDisabled.value) return;

  if (chatStore.groupList.length === 0) {
    await createNewChatGroup();
  }

  chatStore.setStreamIn(true);

  let useModel = selectedApp?.value?.model || chatStore?.activeModel;
  let useModelName =
    usingPlugin?.value?.pluginName ||
    selectedApp?.value?.name ||
    activeModelName.value;

  const useModelType = activeModelKeyType.value;

  if (usingPlugin.value?.deductType && usingPlugin.value?.deductType !== 0) {
    useModel = usingPlugin.value?.parameters;
  }

  let modelAvatar = selectedApp?.value?.coverImg || activeModelAvatar.value;
  let appId;

  if (selectedApp?.value) {
    appId = selectedApp?.value?.id;
  } else {
    appId = activeGroupInfo?.value?.appId;
  }

  if (appId) {
    try {
      const res: any = await fetchQueryOneCatAPI({ id: appId });
      modelAvatar = res.data.modelAvatar;
    } catch (error) {}
  }

  const msg = prompt.value?.trim();
  if (!msg) return;

  await chatStore.setPrompt('');
  nextTick(() => {
    autoResize();
  });

  console.log('开始对话', {
    prompt,
    useModel,
    useModelName,
    useModelType,
    modelAvatar,
    appId,
    pluginParam: usingPlugin.value?.parameters,
  });

  // 调用对话处理函数，使用与Footer组件一致的参数结构
  await onConversation({
    msg: msg,
    action: '',
    model: useModel,
    modelName: useModelName,
    modelType: useModelType,
    modelAvatar: modelAvatar,
    appId: appId || 0,
    extraParam: {},
    fileUrl: '',
    pluginParam: usingPlugin.value?.parameters || ''
  });

  chatStore.setStreamIn(false);
  isUploading.value = false;
};

// 停止生成
const handleStop = () => {
  chatStore.setStreamIn(false);
};

// 文件上传相关
const triggerFileUpload = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  // 文件选择处理逻辑，可以参考Footer组件的实现
  const target = event.target as HTMLInputElement;
  const files = target.files;
  if (files && files.length > 0) {
    // 处理文件上传
    console.log('Selected files:', files);
  }
};

// 监听prompt变化，自动调整高度
watch(prompt, () => {
  nextTick(() => {
    autoResize();
  });
}, { immediate: true });

// 组件挂载后设置焦点
onMounted(() => {
  nextTick(() => {
    if (inputRef.value && !isMobile.value) {
      inputRef.value.focus();
    }
  });
});
</script>

<style scoped>
.centered-input-container {
  @apply bg-gray-50 dark:bg-gray-900;
}

.main-input-container {
  @apply transition-all duration-300;
}

.main-input-container:focus-within {
  @apply shadow-xl;
  transform: translateY(-2px);
}

/* 美化的滚动条样式 */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(99, 102, 241, 0.3));
  border-radius: 6px;
  transition: background 0.3s ease;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.5), rgba(99, 102, 241, 0.5));
}

textarea::-webkit-scrollbar-track {
  background-color: transparent;
}

.dark textarea::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(99, 102, 241, 0.4));
}

.dark textarea::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(99, 102, 241, 0.6));
}

/* 增强的动画效果 */
@keyframes gentle-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

/* 输入框聚焦效果 */
.main-input-container:focus-within .absolute.inset-0 {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(99, 102, 241, 0.03));
}

/* 按钮悬浮效果增强 */
button:hover {
  filter: brightness(1.1);
}

button:active {
  filter: brightness(0.95);
}

/* 快速提示按钮特殊效果 */
.quick-suggestion-btn {
  position: relative;
  overflow: hidden;
}

.quick-suggestion-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.quick-suggestion-btn:hover::before {
  left: 100%;
}

/* 键盘快捷键样式增强 */
kbd {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

kbd:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
</style>