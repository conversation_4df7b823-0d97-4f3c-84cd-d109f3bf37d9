"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookImageEntity = void 0;
const baseEntity_1 = require("../../../common/entity/baseEntity");
const typeorm_1 = require("typeorm");
const storybook_entity_1 = require("./storybook.entity");
const storybook_page_entity_1 = require("./storybook-page.entity");
const storybook_character_entity_1 = require("./storybook-character.entity");
let StorybookImageEntity = class StorybookImageEntity extends baseEntity_1.BaseEntity {
};
__decorate([
    (0, typeorm_1.Column)({ comment: '图片URL', type: 'text' }),
    __metadata("design:type", String)
], StorybookImageEntity.prototype, "imageUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '图片描述', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookImageEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '生成提示词', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookImageEntity.prototype, "prompt", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '使用的模型', nullable: true }),
    __metadata("design:type", String)
], StorybookImageEntity.prototype, "model", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '图片类型(1:角色图,2:页面图,3:封面图)', default: 2 }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "imageType", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '图片质量评级(1-5星)', default: 3, nullable: true }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "qualityRating", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核状态(0:未审核,1:已通过,2:已拒绝)', default: 0 }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "auditStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核备注', nullable: true }),
    __metadata("design:type", String)
], StorybookImageEntity.prototype, "auditRemark", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '所属绘本ID', nullable: true }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "storybookId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '所属页面ID', nullable: true }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "pageId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '所属角色ID', nullable: true }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "characterId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '创建用户ID' }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '图片宽度', nullable: true }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "width", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '图片高度', nullable: true }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "height", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '图片大小(KB)', nullable: true }),
    __metadata("design:type", Number)
], StorybookImageEntity.prototype, "size", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '图片格式', nullable: true }),
    __metadata("design:type", String)
], StorybookImageEntity.prototype, "format", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '生成参数', type: 'json', nullable: true }),
    __metadata("design:type", Object)
], StorybookImageEntity.prototype, "generationParams", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => storybook_entity_1.StorybookEntity, storybook => storybook.id, { onDelete: 'SET NULL', nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'storybookId' }),
    __metadata("design:type", storybook_entity_1.StorybookEntity)
], StorybookImageEntity.prototype, "storybook", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => storybook_page_entity_1.StorybookPageEntity, page => page.id, { onDelete: 'SET NULL', nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'pageId' }),
    __metadata("design:type", storybook_page_entity_1.StorybookPageEntity)
], StorybookImageEntity.prototype, "page", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => storybook_character_entity_1.StorybookCharacterEntity, character => character.id, { onDelete: 'SET NULL', nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'characterId' }),
    __metadata("design:type", storybook_character_entity_1.StorybookCharacterEntity)
], StorybookImageEntity.prototype, "character", void 0);
StorybookImageEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'storybook_image' })
], StorybookImageEntity);
exports.StorybookImageEntity = StorybookImageEntity;
