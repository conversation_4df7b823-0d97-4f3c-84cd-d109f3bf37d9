"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookCharacterEntity = void 0;
const baseEntity_1 = require("../../../common/entity/baseEntity");
const typeorm_1 = require("typeorm");
const storybook_entity_1 = require("./storybook.entity");
let StorybookCharacterEntity = class StorybookCharacterEntity extends baseEntity_1.BaseEntity {
};
__decorate([
    (0, typeorm_1.Column)({ comment: '所属绘本ID', nullable: true }),
    __metadata("design:type", Number)
], StorybookCharacterEntity.prototype, "storybookId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '创建者用户ID', nullable: true }),
    __metadata("design:type", Number)
], StorybookCharacterEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '角色名称' }),
    __metadata("design:type", String)
], StorybookCharacterEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '角色类型', nullable: true }),
    __metadata("design:type", String)
], StorybookCharacterEntity.prototype, "characterType", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '外观描述', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookCharacterEntity.prototype, "appearance", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '性格特点', type: 'json', nullable: true }),
    __metadata("design:type", Object)
], StorybookCharacterEntity.prototype, "personalityTraits", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '外观特点', type: 'json', nullable: true }),
    __metadata("design:type", Object)
], StorybookCharacterEntity.prototype, "appearanceTraits", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '角色图片URL', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookCharacterEntity.prototype, "imageUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否为模板(0:否,1:是)', default: 0 }),
    __metadata("design:type", Number)
], StorybookCharacterEntity.prototype, "isTemplate", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '角色标签', type: 'json', nullable: true }),
    __metadata("design:type", Array)
], StorybookCharacterEntity.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否收藏(0:否,1:是)', default: 0 }),
    __metadata("design:type", Number)
], StorybookCharacterEntity.prototype, "isFavorite", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '角色在故事中的角色', nullable: true }),
    __metadata("design:type", String)
], StorybookCharacterEntity.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => storybook_entity_1.StorybookEntity, storybook => storybook.characters, { onDelete: 'CASCADE', nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'storybookId' }),
    __metadata("design:type", storybook_entity_1.StorybookEntity)
], StorybookCharacterEntity.prototype, "storybook", void 0);
StorybookCharacterEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'storybook_character' })
], StorybookCharacterEntity);
exports.StorybookCharacterEntity = StorybookCharacterEntity;
