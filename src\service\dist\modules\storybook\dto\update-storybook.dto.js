"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateStorybookDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UpdateStorybookDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '绘本标题', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateStorybookDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '绘本描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateStorybookDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '封面图片URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateStorybookDto.prototype, "coverImg", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否公开(0:私有,1:公开)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateStorybookDto.prototype, "isPublic", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态(0:草稿,1:已发布,2:审核中,3:已拒绝)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateStorybookDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '绘本内容JSON', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateStorybookDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '来源', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateStorybookDto.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签', required: false, type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], UpdateStorybookDto.prototype, "tags", void 0);
exports.UpdateStorybookDto = UpdateStorybookDto;
