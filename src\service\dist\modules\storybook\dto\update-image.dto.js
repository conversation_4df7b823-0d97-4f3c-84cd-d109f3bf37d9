"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateImageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UpdateImageDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateImageDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateImageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成提示词', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateImageDto.prototype, "prompt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用的模型', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateImageDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片类型(1:角色图,2:页面图,3:封面图)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateImageDto.prototype, "imageType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片质量评级(1-5星)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateImageDto.prototype, "qualityRating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核状态(0:未审核,1:已通过,2:已拒绝)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateImageDto.prototype, "auditStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '审核备注', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateImageDto.prototype, "auditRemark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属绘本ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateImageDto.prototype, "storybookId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属页面ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateImageDto.prototype, "pageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属角色ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateImageDto.prototype, "characterId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片宽度', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateImageDto.prototype, "width", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片高度', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateImageDto.prototype, "height", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片大小(KB)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateImageDto.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片格式', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateImageDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成参数', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateImageDto.prototype, "generationParams", void 0);
exports.UpdateImageDto = UpdateImageDto;
