"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var StorybookService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const badWords_service_1 = require("../badWords/badWords.service");
const core_1 = require("@nestjs/core");
const entities_1 = require("./entities");
let StorybookService = StorybookService_1 = class StorybookService {
    constructor(storybookRepository, pageRepository, characterRepository, templateRepository, statisticRepository, promptRepository, configRepository, imageRepository, folderRepository, badWordsService, request) {
        this.storybookRepository = storybookRepository;
        this.pageRepository = pageRepository;
        this.characterRepository = characterRepository;
        this.templateRepository = templateRepository;
        this.statisticRepository = statisticRepository;
        this.promptRepository = promptRepository;
        this.configRepository = configRepository;
        this.imageRepository = imageRepository;
        this.folderRepository = folderRepository;
        this.badWordsService = badWordsService;
        this.request = request;
        this.logger = new common_1.Logger(StorybookService_1.name);
    }
    async getConfig(key) {
        return this.configRepository.findOne({ where: { configKey: key } });
    }
    async getAllConfigs() {
        return this.configRepository.find();
    }
    async setConfig(key, value, description) {
        const existingConfig = await this.configRepository.findOne({ where: { configKey: key } });
        if (existingConfig) {
            existingConfig.configVal = value;
            if (description) {
                existingConfig.description = description;
            }
            return this.configRepository.save(existingConfig);
        }
        else {
            const newConfig = this.configRepository.create({
                configKey: key,
                configVal: value,
                description: description || `Configuration for ${key}`,
            });
            return this.configRepository.save(newConfig);
        }
    }
    async onModuleInit() {
        await this.initDefaultConfigs();
        this.logger.log('Storybook module initialized');
    }
    async initDefaultConfigs() {
        const defaultConfigs = [
            { key: 'storybook_enabled', value: '1', description: '是否启用绘本创作功能(0:禁用,1:启用)' },
            { key: 'storybook_public_review', value: '1', description: '公开绘本是否需要审核(0:不需要,1:需要)' },
            { key: 'storybook_default_model', value: 'gpt-4o', description: '绘本创作默认使用的AI模型' },
            { key: 'storybook_image_model', value: 'gpt-image-1', description: '绘本创作默认使用的图像生成模型' },
            { key: 'storybook_image_size', value: '1024x1024', description: '绘本创作默认使用的图像尺寸' },
            { key: 'storybook_image_quality', value: 'hd', description: '绘本创作默认使用的图像质量' },
            { key: 'storybook_image_style', value: 'natural', description: '绘本创作默认使用的图像风格' },
            { key: 'storybook_image_fallback_service', value: 'https://image.pollinations.ai/prompt/%s', description: '备用图像生成服务' },
            { key: 'storybook_image_generation_enabled', value: '1', description: '是否启用图像生成功能(0:禁用,1:启用)' },
            { key: 'storybook_max_pages', value: '20', description: '单本绘本最大页数' },
            { key: 'storybook_daily_limit', value: '5', description: '用户每日可创建绘本数量限制' },
            { key: 'storybook_auto_audit', value: '1', description: '是否启用绘本自动审核(0:禁用,1:启用)' },
            { key: 'storybook_sensitive_filter', value: '1', description: '是否启用敏感词过滤(0:禁用,1:启用)' },
        ];
        for (const config of defaultConfigs) {
            const existing = await this.getConfig(config.key);
            if (!existing) {
                await this.setConfig(config.key, config.value, config.description);
                this.logger.log(`Created default config: ${config.key}`);
            }
        }
    }
    async getAllStorybooks(options = {}) {
        const { page = 1, limit = 20, status, userId, keyword, startDate, endDate } = options;
        const queryBuilder = this.storybookRepository.createQueryBuilder('storybook');
        if (status !== undefined) {
            queryBuilder.andWhere('storybook.status = :status', { status });
        }
        if (userId) {
            queryBuilder.andWhere('storybook.userId = :userId', { userId });
        }
        if (keyword) {
            queryBuilder.andWhere('(storybook.title LIKE :keyword OR storybook.description LIKE :keyword)', { keyword: `%${keyword}%` });
        }
        if (startDate) {
            queryBuilder.andWhere('storybook.createdAt >= :startDate', { startDate });
        }
        if (endDate) {
            queryBuilder.andWhere('storybook.createdAt <= :endDate', { endDate });
        }
        const total = await queryBuilder.getCount();
        const items = await queryBuilder
            .orderBy('storybook.updatedAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit)
            .getMany();
        return { items, total };
    }
    async reviewStorybook(id, status) {
        const storybook = await this.storybookRepository.findOne({
            where: { id }
        });
        if (!storybook) {
            throw new common_1.NotFoundException('绘本不存在');
        }
        if (storybook.status !== 2) {
            throw new common_1.BadRequestException('只能审核状态为"审核中"的绘本');
        }
        storybook.status = status;
        return this.storybookRepository.save(storybook);
    }
    async setRecommendStatus(id, isRecommended) {
        const storybook = await this.storybookRepository.findOne({
            where: { id }
        });
        if (!storybook) {
            throw new common_1.NotFoundException('绘本不存在');
        }
        if (storybook.status !== 1 || storybook.isPublic !== 1) {
            throw new common_1.BadRequestException('只能推荐已发布且公开的绘本');
        }
        storybook.isRecommended = isRecommended;
        return this.storybookRepository.save(storybook);
    }
    async createPage(createPageDto) {
        const storybook = await this.storybookRepository.findOne({
            where: { id: createPageDto.storybookId }
        });
        if (!storybook) {
            throw new common_1.NotFoundException('绘本不存在');
        }
        const maxPagesConfig = await this.getConfig('storybook_max_pages');
        const maxPages = maxPagesConfig ? parseInt(maxPagesConfig.configVal) : 20;
        const pageCount = await this.pageRepository.count({
            where: { storybookId: createPageDto.storybookId }
        });
        if (pageCount >= maxPages) {
            throw new common_1.BadRequestException(`页数已达上限(${maxPages}页)`);
        }
        if (createPageDto.text) {
            const textCheck = await this.checkContent(createPageDto.text, storybook.userId);
            if (!textCheck.safe) {
                throw new common_1.HttpException('页面文本包含敏感内容，请修改后重试', common_1.HttpStatus.BAD_REQUEST);
            }
        }
        if (createPageDto.imageDescription) {
            const descriptionCheck = await this.checkContent(createPageDto.imageDescription, storybook.userId);
            if (!descriptionCheck.safe) {
                throw new common_1.HttpException('图片描述包含敏感内容，请修改后重试', common_1.HttpStatus.BAD_REQUEST);
            }
        }
        const page = this.pageRepository.create(createPageDto);
        return this.pageRepository.save(page);
    }
    async updatePage(id, updateData) {
        const page = await this.pageRepository.findOne({
            where: { id },
            relations: ['storybook']
        });
        if (!page) {
            throw new common_1.NotFoundException('页面不存在');
        }
        if (updateData.text) {
            const textCheck = await this.checkContent(updateData.text, page.storybook.userId);
            if (!textCheck.safe) {
                throw new common_1.HttpException('页面文本包含敏感内容，请修改后重试', common_1.HttpStatus.BAD_REQUEST);
            }
        }
        if (updateData.imageDescription) {
            const descriptionCheck = await this.checkContent(updateData.imageDescription, page.storybook.userId);
            if (!descriptionCheck.safe) {
                throw new common_1.HttpException('图片描述包含敏感内容，请修改后重试', common_1.HttpStatus.BAD_REQUEST);
            }
        }
        Object.assign(page, updateData);
        return this.pageRepository.save(page);
    }
    async deletePage(id) {
        const page = await this.pageRepository.findOne({
            where: { id }
        });
        if (!page) {
            throw new common_1.NotFoundException('页面不存在');
        }
        await this.pageRepository.remove(page);
    }
    async getStorybookPages(storybookId) {
        return this.pageRepository.find({
            where: { storybookId },
            order: { pageNumber: 'ASC' }
        });
    }
    async createCharacter(createCharacterDto) {
        var _a, _b;
        if (createCharacterDto.storybookId) {
            const storybook = await this.storybookRepository.findOne({
                where: { id: createCharacterDto.storybookId }
            });
            if (!storybook) {
                throw new common_1.NotFoundException('绘本不存在');
            }
        }
        const userId = (_b = (_a = this.request) === null || _a === void 0 ? void 0 : _a.user) === null || _b === void 0 ? void 0 : _b.id;
        if (userId) {
            createCharacterDto['userId'] = userId;
        }
        if (createCharacterDto.tags && !Array.isArray(createCharacterDto.tags)) {
            try {
                createCharacterDto.tags = JSON.parse(createCharacterDto.tags);
            }
            catch (e) {
                createCharacterDto.tags = [];
            }
        }
        const character = this.characterRepository.create(createCharacterDto);
        return this.characterRepository.save(character);
    }
    async updateCharacter(id, updateData) {
        const character = await this.characterRepository.findOne({
            where: { id }
        });
        if (!character) {
            throw new common_1.NotFoundException('角色不存在');
        }
        Object.assign(character, updateData);
        return this.characterRepository.save(character);
    }
    async deleteCharacter(id) {
        const character = await this.characterRepository.findOne({
            where: { id }
        });
        if (!character) {
            throw new common_1.NotFoundException('角色不存在');
        }
        await this.characterRepository.remove(character);
    }
    async getStorybookCharacters(storybookId) {
        return this.characterRepository.find({
            where: { storybookId }
        });
    }
    async getCharacterTemplates() {
        return this.characterRepository.find({
            where: { isTemplate: 1, storybookId: (0, typeorm_2.IsNull)() }
        });
    }
    async getUserCharacters(userId) {
        return this.characterRepository.find({
            where: [
                { userId, storybookId: (0, typeorm_2.IsNull)() },
                { userId, isTemplate: 0 }
            ],
            order: { updatedAt: 'DESC' }
        });
    }
    async getAllPrompts() {
        return this.promptRepository.find({
            order: { order: 'DESC' },
        });
    }
    async createPrompt(data) {
        const prompt = this.promptRepository.create(data);
        return this.promptRepository.save(prompt);
    }
    async updatePrompt(id, data) {
        const prompt = await this.promptRepository.findOne({
            where: { id },
        });
        if (!prompt) {
            return { success: false, message: '提示词不存在' };
        }
        Object.assign(prompt, data);
        return this.promptRepository.save(prompt);
    }
    async deletePrompt(id) {
        const prompt = await this.promptRepository.findOne({
            where: { id },
        });
        if (!prompt) {
            return { success: false, message: '提示词不存在' };
        }
        await this.promptRepository.remove(prompt);
        return { success: true, message: '删除成功' };
    }
    async getAllTemplates() {
        return this.templateRepository.find({
            order: { order: 'DESC' },
        });
    }
    async createTemplate(data) {
        const template = this.templateRepository.create(data);
        return this.templateRepository.save(template);
    }
    async updateTemplate(id, data) {
        const template = await this.templateRepository.findOne({
            where: { id },
        });
        if (!template) {
            return { success: false, message: '模板不存在' };
        }
        Object.assign(template, data);
        return this.templateRepository.save(template);
    }
    async deleteTemplate(id) {
        const template = await this.templateRepository.findOne({
            where: { id },
        });
        if (!template) {
            return { success: false, message: '模板不存在' };
        }
        await this.templateRepository.remove(template);
        return { success: true, message: '删除成功' };
    }
    async getStatistics(startDate, endDate) {
        const start = startDate ? new Date(startDate) : new Date();
        start.setDate(start.getDate() - 30);
        const end = endDate ? new Date(endDate) : new Date();
        const totalStorybooks = await this.storybookRepository.count();
        const publicStorybooks = await this.storybookRepository.count({
            where: { isPublic: 1, status: 1 },
        });
        const pendingReviewStorybooks = await this.storybookRepository.count({
            where: { status: 2 },
        });
        const recommendedStorybooks = await this.storybookRepository.count({
            where: { isRecommended: 1 },
        });
        const newStorybooks = await this.storybookRepository.count({
            where: {
                createdAt: (0, typeorm_2.Between)(start, end),
            },
        });
        const totalViews = await this.storybookRepository
            .createQueryBuilder('storybook')
            .select('SUM(storybook.viewCount)', 'totalViews')
            .getRawOne();
        const totalLikes = await this.storybookRepository
            .createQueryBuilder('storybook')
            .select('SUM(storybook.likeCount)', 'totalLikes')
            .getRawOne();
        const totalImages = await this.imageRepository.count();
        const pendingAuditImages = await this.imageRepository.count({
            where: { auditStatus: 0 },
        });
        const rejectedImages = await this.imageRepository.count({
            where: { auditStatus: 2 },
        });
        return {
            totalStorybooks,
            publicStorybooks,
            pendingReviewStorybooks,
            recommendedStorybooks,
            newStorybooks,
            totalViews: totalViews.totalViews || 0,
            totalLikes: totalLikes.totalLikes || 0,
            totalImages,
            pendingAuditImages,
            rejectedImages,
        };
    }
    async createImage(userId, createImageDto) {
        const image = this.imageRepository.create(Object.assign(Object.assign({}, createImageDto), { userId }));
        return this.imageRepository.save(image);
    }
    async getImages(options = {}) {
        const { page = 1, limit = 20, userId, imageType, auditStatus, storybookId, pageId, characterId, keyword, startDate, endDate } = options;
        const queryBuilder = this.imageRepository.createQueryBuilder('image');
        if (userId) {
            queryBuilder.andWhere('image.userId = :userId', { userId });
        }
        if (imageType !== undefined) {
            queryBuilder.andWhere('image.imageType = :imageType', { imageType });
        }
        if (auditStatus !== undefined) {
            queryBuilder.andWhere('image.auditStatus = :auditStatus', { auditStatus });
        }
        if (storybookId) {
            queryBuilder.andWhere('image.storybookId = :storybookId', { storybookId });
        }
        if (pageId) {
            queryBuilder.andWhere('image.pageId = :pageId', { pageId });
        }
        if (characterId) {
            queryBuilder.andWhere('image.characterId = :characterId', { characterId });
        }
        if (keyword) {
            queryBuilder.andWhere('(image.description LIKE :keyword OR image.prompt LIKE :keyword)', { keyword: `%${keyword}%` });
        }
        if (startDate) {
            queryBuilder.andWhere('image.createdAt >= :startDate', { startDate });
        }
        if (endDate) {
            queryBuilder.andWhere('image.createdAt <= :endDate', { endDate });
        }
        const total = await queryBuilder.getCount();
        const items = await queryBuilder
            .orderBy('image.createdAt', 'DESC')
            .skip((page - 1) * limit)
            .take(limit)
            .getMany();
        return { items, total };
    }
    async getImageDetail(id) {
        const image = await this.imageRepository.findOne({
            where: { id },
            relations: ['storybook', 'page', 'character']
        });
        if (!image) {
            throw new common_1.NotFoundException('图像不存在');
        }
        return image;
    }
    async updateImage(id, updateImageDto) {
        const image = await this.imageRepository.findOne({
            where: { id }
        });
        if (!image) {
            throw new common_1.NotFoundException('图像不存在');
        }
        Object.assign(image, updateImageDto);
        return this.imageRepository.save(image);
    }
    async deleteImage(id) {
        const image = await this.imageRepository.findOne({
            where: { id }
        });
        if (!image) {
            throw new common_1.NotFoundException('图像不存在');
        }
        await this.imageRepository.remove(image);
    }
    async auditImage(id, auditStatus, auditRemark) {
        const image = await this.imageRepository.findOne({
            where: { id }
        });
        if (!image) {
            throw new common_1.NotFoundException('图像不存在');
        }
        image.auditStatus = auditStatus;
        if (auditRemark) {
            image.auditRemark = auditRemark;
        }
        return this.imageRepository.save(image);
    }
    async setImageQuality(id, qualityRating) {
        const image = await this.imageRepository.findOne({
            where: { id }
        });
        if (!image) {
            throw new common_1.NotFoundException('图像不存在');
        }
        if (qualityRating < 1 || qualityRating > 5) {
            throw new common_1.BadRequestException('质量评级必须在1-5之间');
        }
        image.qualityRating = qualityRating;
        return this.imageRepository.save(image);
    }
    async getImageGenerationConfig() {
        const modelConfig = await this.getConfig('storybook_image_model');
        const model = modelConfig ? modelConfig.configVal : 'gpt-image-1';
        const sizeConfig = await this.getConfig('storybook_image_size');
        const size = sizeConfig ? sizeConfig.configVal : '1024x1024';
        const qualityConfig = await this.getConfig('storybook_image_quality');
        const quality = qualityConfig ? qualityConfig.configVal : 'hd';
        const styleConfig = await this.getConfig('storybook_image_style');
        const style = styleConfig ? styleConfig.configVal : 'natural';
        const fallbackServiceConfig = await this.getConfig('storybook_image_fallback_service');
        const fallbackService = fallbackServiceConfig ? fallbackServiceConfig.configVal : 'https://image.pollinations.ai/prompt/%s';
        const enabledConfig = await this.getConfig('storybook_image_generation_enabled');
        const enabled = enabledConfig ? enabledConfig.configVal === '1' : true;
        return {
            model,
            size,
            quality,
            style,
            fallbackService,
            enabled
        };
    }
    async updateImageGenerationConfig(config) {
        const { model, size, quality, style, fallbackService, enabled } = config;
        if (model) {
            await this.setConfig('storybook_image_model', model, '绘本创作默认使用的图像生成模型');
        }
        if (size) {
            await this.setConfig('storybook_image_size', size, '绘本创作默认使用的图像尺寸');
        }
        if (quality) {
            await this.setConfig('storybook_image_quality', quality, '绘本创作默认使用的图像质量');
        }
        if (style) {
            await this.setConfig('storybook_image_style', style, '绘本创作默认使用的图像风格');
        }
        if (fallbackService) {
            await this.setConfig('storybook_image_fallback_service', fallbackService, '备用图像生成服务');
        }
        if (enabled !== undefined) {
            await this.setConfig('storybook_image_generation_enabled', enabled ? '1' : '0', '是否启用图像生成功能(0:禁用,1:启用)');
        }
        return this.getImageGenerationConfig();
    }
    async getImageUsageStats(startDate, endDate) {
        const start = startDate ? new Date(startDate) : new Date();
        start.setDate(start.getDate() - 30);
        const end = endDate ? new Date(endDate) : new Date();
        const totalImages = await this.imageRepository.count({
            where: {
                createdAt: (0, typeorm_2.Between)(start, end),
            },
        });
        const characterImages = await this.imageRepository.count({
            where: {
                imageType: 1,
                createdAt: (0, typeorm_2.Between)(start, end),
            },
        });
        const pageImages = await this.imageRepository.count({
            where: {
                imageType: 2,
                createdAt: (0, typeorm_2.Between)(start, end),
            },
        });
        const coverImages = await this.imageRepository.count({
            where: {
                imageType: 3,
                createdAt: (0, typeorm_2.Between)(start, end),
            },
        });
        const modelStats = await this.imageRepository
            .createQueryBuilder('image')
            .select('image.model', 'model')
            .addSelect('COUNT(image.id)', 'count')
            .where('image.createdAt BETWEEN :start AND :end', { start, end })
            .groupBy('image.model')
            .getRawMany();
        return {
            totalImages,
            characterImages,
            pageImages,
            coverImages,
            modelStats
        };
    }
    async checkContent(content, userId) {
        try {
            const triggeredWords = await this.badWordsService.checkBadWords(content, userId);
            return {
                safe: triggeredWords.length === 0,
                triggeredWords
            };
        }
        catch (error) {
            this.logger.error(`内容安全检查失败: ${error.message}`, error.stack);
            return {
                safe: false,
                triggeredWords: ['违规内容']
            };
        }
    }
    async auditStorybookContent(id, userId) {
        const storybook = await this.storybookRepository.findOne({
            where: { id },
            relations: ['pages']
        });
        if (!storybook) {
            throw new common_1.NotFoundException('绘本不存在');
        }
        const issues = [];
        let safe = true;
        const titleCheck = await this.checkContent(storybook.title, userId);
        if (!titleCheck.safe) {
            safe = false;
            issues.push({
                type: 'title',
                content: storybook.title,
                triggeredWords: titleCheck.triggeredWords
            });
        }
        if (storybook.description) {
            const descriptionCheck = await this.checkContent(storybook.description, userId);
            if (!descriptionCheck.safe) {
                safe = false;
                issues.push({
                    type: 'description',
                    content: storybook.description,
                    triggeredWords: descriptionCheck.triggeredWords
                });
            }
        }
        if (storybook.pages && storybook.pages.length > 0) {
            for (const page of storybook.pages) {
                if (page.text) {
                    const textCheck = await this.checkContent(page.text, userId);
                    if (!textCheck.safe) {
                        safe = false;
                        issues.push({
                            type: 'page',
                            pageId: page.id,
                            pageNumber: page.pageNumber,
                            content: page.text,
                            triggeredWords: textCheck.triggeredWords
                        });
                    }
                }
                if (page.imageDescription) {
                    const descriptionCheck = await this.checkContent(page.imageDescription, userId);
                    if (!descriptionCheck.safe) {
                        safe = false;
                        issues.push({
                            type: 'pageImageDescription',
                            pageId: page.id,
                            pageNumber: page.pageNumber,
                            content: page.imageDescription,
                            triggeredWords: descriptionCheck.triggeredWords
                        });
                    }
                }
            }
        }
        return { safe, issues };
    }
    async autoAuditStorybook(id, userId) {
        const storybook = await this.storybookRepository.findOne({
            where: { id }
        });
        if (!storybook) {
            throw new common_1.NotFoundException('绘本不存在');
        }
        if (storybook.userId !== userId) {
            throw new common_1.BadRequestException('无权操作此绘本');
        }
        if (storybook.status !== 2) {
            throw new common_1.BadRequestException('只能审核状态为"审核中"的绘本');
        }
        const auditResult = await this.auditStorybookContent(id, userId);
        if (auditResult.safe) {
            storybook.status = 1;
        }
        else {
            storybook.status = 3;
            const reasons = auditResult.issues.map(issue => `${issue.type === 'title' ? '标题' :
                issue.type === 'description' ? '描述' :
                    issue.type === 'page' ? `第${issue.pageNumber}页文本` :
                        `第${issue.pageNumber}页图片描述`} 包含敏感词: ${issue.triggeredWords.join(', ')}`).join('; ');
            storybook.description = `${storybook.description || ''}\n\n审核失败原因: ${reasons}`;
        }
        return this.storybookRepository.save(storybook);
    }
    async getContentSafetyConfig() {
        const autoAuditConfig = await this.getConfig('storybook_auto_audit');
        const autoAudit = autoAuditConfig ? autoAuditConfig.configVal === '1' : false;
        const sensitiveFilterConfig = await this.getConfig('storybook_sensitive_filter');
        const sensitiveFilter = sensitiveFilterConfig ? sensitiveFilterConfig.configVal === '1' : true;
        return {
            autoAudit,
            sensitiveFilter
        };
    }
    async updateContentSafetyConfig(config) {
        const { autoAudit, sensitiveFilter } = config;
        if (autoAudit !== undefined) {
            await this.setConfig('storybook_auto_audit', autoAudit ? '1' : '0', '是否启用绘本自动审核(0:禁用,1:启用)');
        }
        if (sensitiveFilter !== undefined) {
            await this.setConfig('storybook_sensitive_filter', sensitiveFilter ? '1' : '0', '是否启用敏感词过滤(0:禁用,1:启用)');
        }
        return this.getContentSafetyConfig();
    }
    async filterSensitiveContent(content, userId) {
        try {
            const sensitiveFilterConfig = await this.getConfig('storybook_sensitive_filter');
            const sensitiveFilter = sensitiveFilterConfig ? sensitiveFilterConfig.configVal === '1' : true;
            if (!sensitiveFilter) {
                return content;
            }
            const triggeredWords = await this.badWordsService.checkBadWords(content, userId);
            if (triggeredWords.length === 0) {
                return content;
            }
            let filteredContent = content;
            for (const word of triggeredWords) {
                const regex = new RegExp(word, 'g');
                filteredContent = filteredContent.replace(regex, '*'.repeat(word.length));
            }
            return filteredContent;
        }
        catch (error) {
            this.logger.error(`敏感词过滤失败: ${error.message}`, error.stack);
            return content;
        }
    }
    async getViolationStats(startDate, endDate) {
        const start = startDate ? new Date(startDate) : new Date();
        start.setDate(start.getDate() - 30);
        const end = endDate ? new Date(endDate) : new Date();
        const rejectedStorybooks = await this.storybookRepository.count({
            where: {
                status: 3,
                updatedAt: (0, typeorm_2.Between)(start, end)
            }
        });
        const pendingStorybooks = await this.storybookRepository.count({
            where: {
                status: 2,
                updatedAt: (0, typeorm_2.Between)(start, end)
            }
        });
        const rejectedImages = await this.imageRepository.count({
            where: {
                auditStatus: 2,
                updatedAt: (0, typeorm_2.Between)(start, end)
            }
        });
        return {
            rejectedStorybooks,
            pendingStorybooks,
            rejectedImages,
            period: {
                start,
                end
            }
        };
    }
};
StorybookService = StorybookService_1 = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.REQUEST }),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.StorybookEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.StorybookPageEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(entities_1.StorybookCharacterEntity)),
    __param(3, (0, typeorm_1.InjectRepository)(entities_1.StorybookTemplateEntity)),
    __param(4, (0, typeorm_1.InjectRepository)(entities_1.StorybookStatisticEntity)),
    __param(5, (0, typeorm_1.InjectRepository)(entities_1.StorybookPromptEntity)),
    __param(6, (0, typeorm_1.InjectRepository)(entities_1.StorybookConfigEntity)),
    __param(7, (0, typeorm_1.InjectRepository)(entities_1.StorybookImageEntity)),
    __param(8, (0, typeorm_1.InjectRepository)(entities_1.StorybookFolderEntity)),
    __param(10, (0, common_1.Inject)(core_1.REQUEST)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        badWords_service_1.BadWordsService, Object])
], StorybookService);
exports.StorybookService = StorybookService;
