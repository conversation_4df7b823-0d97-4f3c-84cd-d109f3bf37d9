<template>
  <div class="quick-start-sidebar h-full bg-white/98 dark:bg-gray-800/98 rounded-xl ds-shadow-xl overflow-hidden backdrop-blur-md border border-white/30 dark:border-gray-700/40 relative group">
    <!-- 美化的背景装饰层 - 教育主题 -->
    <div class="absolute inset-0 pointer-events-none overflow-hidden">
      <!-- 主背景渐变 -->
      <div class="absolute inset-0 bg-gradient-to-br from-purple-50/20 via-blue-50/15 to-indigo-50/10 dark:from-purple-900/15 dark:via-blue-900/10 dark:to-indigo-900/8"></div>

      <!-- 教育主题装饰点 -->
      <div class="absolute top-8 right-12 w-2 h-2 rounded-full bg-purple-400/30 dark:bg-purple-400/40 animate-pulse"></div>
      <div class="absolute top-20 right-20 w-1 h-1 rounded-full bg-indigo-400/35 dark:bg-indigo-400/45"></div>
      <div class="absolute bottom-24 left-12 w-3 h-3 rounded-full bg-blue-400/25 dark:bg-blue-400/35"></div>
      <div class="absolute top-1/3 left-8 w-1.5 h-1.5 rounded-full bg-green-400/30 dark:bg-green-400/40"></div>
      <div class="absolute bottom-1/3 right-10 w-2.5 h-2.5 rounded-full bg-yellow-400/25 dark:bg-yellow-400/35"></div>

      <!-- 教育图标装饰 -->
      <div class="absolute top-12 left-10 w-4 h-4 opacity-[0.03] dark:opacity-[0.05] transform rotate-12">
        <svg viewBox="0 0 24 24" fill="currentColor" class="w-full h-full text-purple-600 dark:text-purple-400">
          <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z"/>
        </svg>
      </div>

      <!-- 顶部装饰光线 -->
      <div class="absolute top-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-purple-400/30 to-transparent dark:via-purple-300/40"></div>
    </div>
    <!-- 美化的头部区域 -->
    <div class="sidebar-header relative overflow-hidden">
      <!-- 头部渐变背景 -->
      <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-indigo-500/8 to-purple-500/6 dark:from-blue-600/20 dark:via-indigo-600/15 dark:to-purple-600/10"></div>

      <!-- 装饰性背景图案 -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-4 -right-4 w-16 h-16 rounded-full bg-gradient-to-br from-blue-400/20 to-indigo-500/20 blur-xl"></div>
        <div class="absolute top-6 -left-2 w-8 h-8 rounded-full bg-gradient-to-br from-purple-400/15 to-pink-500/15 blur-lg"></div>
      </div>

      <!-- 主要内容 -->
      <div class="relative z-10 p-6 border-b border-white/30 dark:border-gray-600/30">
        <div class="flex items-center mb-4">
          <!-- 优化的图标容器 -->
          <div class="relative mr-3">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 dark:shadow-blue-600/30 transform transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-blue-500/30">
              <!-- 增强的闪电图标 -->
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-white drop-shadow-sm" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <!-- 小光点装饰 -->
            <div class="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse shadow-sm"></div>
          </div>

          <!-- 优化的标题 -->
          <div class="flex-1">
            <h2 class="text-xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-900 dark:from-gray-100 dark:via-white dark:to-gray-200 bg-clip-text text-transparent leading-tight">
              快速开始
            </h2>
            <div class="text-xs font-medium text-blue-600 dark:text-blue-400 mt-0.5 tracking-wide uppercase opacity-80">
              AI Teaching Assistant
            </div>
          </div>
        </div>

        <!-- 优化的描述文字 -->
        <div class="relative">
          <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
            选择一个教学模板快速开始您的
            <span class="font-semibold text-blue-600 dark:text-blue-400">AI教学体验</span>
          </p>
          <!-- 下划线装饰 -->
          <div class="absolute bottom-0 left-0 w-12 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2 opacity-60"></div>
        </div>
      </div>
    </div>

    <!-- 快速开始选项 -->
    <div class="sidebar-content p-6 overflow-y-auto flex-1">
      <!-- 基础教学类 -->
      <div class="category-section mb-6">
        <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3 flex items-center">
          <span class="text-lg mr-2">📖</span>
          基础教学
        </h3>
        <div class="space-y-3">
          <button
            v-for="prompt in basicTeachingPrompts"
            :key="prompt.text"
            @click="$emit('use-prompt', prompt.text)"
            class="relative w-full text-left p-4 bg-gradient-to-br from-blue-50 via-blue-100/80 to-blue-150/60 dark:from-blue-900/30 dark:via-blue-800/25 dark:to-blue-700/20 rounded-xl border border-blue-200/60 dark:border-blue-600/40 hover:border-blue-300/80 dark:hover:border-blue-500/60 shadow-sm hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-blue-600/30 transition-all duration-300 group hover:scale-[1.02] hover:-translate-y-0.5 overflow-hidden backdrop-blur-sm education-glow"
          >
            <!-- 微妙的背景光晕 -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div class="relative flex items-start">
              <span class="text-2xl mr-4 group-hover:scale-125 transition-all duration-300 filter group-hover:brightness-110">{{ prompt.emoji }}</span>
              <div class="flex-1">
                <div class="font-semibold text-gray-800 dark:text-gray-200 mb-1 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-200">{{ prompt.title }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{{ prompt.description }}</div>
              </div>
              <!-- 右侧装饰图标 -->
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 ml-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </button>
        </div>
      </div>

      <!-- 课程设计类 -->
      <div class="category-section mb-6">
        <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3 flex items-center">
          <span class="text-lg mr-2">📝</span>
          课程设计
        </h3>
        <div class="space-y-3">
          <button
            v-for="prompt in courseDesignPrompts"
            :key="prompt.text"
            @click="$emit('use-prompt', prompt.text)"
            class="relative w-full text-left p-4 bg-gradient-to-br from-green-50 via-green-100/80 to-green-150/60 dark:from-green-900/30 dark:via-green-800/25 dark:to-green-700/20 rounded-xl border border-green-200/60 dark:border-green-600/40 hover:border-green-300/80 dark:hover:border-green-500/60 shadow-sm hover:shadow-lg hover:shadow-green-500/20 dark:hover:shadow-green-600/30 transition-all duration-300 group hover:scale-[1.02] hover:-translate-y-0.5 overflow-hidden backdrop-blur-sm"
          >
            <!-- 微妙的背景光晕 -->
            <div class="absolute inset-0 bg-gradient-to-br from-green-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div class="relative flex items-start">
              <span class="text-2xl mr-4 group-hover:scale-125 transition-all duration-300 filter group-hover:brightness-110">{{ prompt.emoji }}</span>
              <div class="flex-1">
                <div class="font-semibold text-gray-800 dark:text-gray-200 mb-1 group-hover:text-green-700 dark:group-hover:text-green-300 transition-colors duration-200">{{ prompt.title }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{{ prompt.description }}</div>
              </div>
              <!-- 右侧装饰图标 -->
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 ml-2">
                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </button>
        </div>
      </div>

      <!-- 教学方法类 -->
      <div class="category-section mb-6">
        <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3 flex items-center">
          <span class="text-lg mr-2">🎯</span>
          教学方法
        </h3>
        <div class="space-y-3">
          <button
            v-for="prompt in teachingMethodPrompts"
            :key="prompt.text"
            @click="$emit('use-prompt', prompt.text)"
            class="relative w-full text-left p-4 bg-gradient-to-br from-purple-50 via-purple-100/80 to-purple-150/60 dark:from-purple-900/30 dark:via-purple-800/25 dark:to-purple-700/20 rounded-xl border border-purple-200/60 dark:border-purple-600/40 hover:border-purple-300/80 dark:hover:border-purple-500/60 shadow-sm hover:shadow-lg hover:shadow-purple-500/20 dark:hover:shadow-purple-600/30 transition-all duration-300 group hover:scale-[1.02] hover:-translate-y-0.5 overflow-hidden backdrop-blur-sm"
          >
            <!-- 微妙的背景光晕 -->
            <div class="absolute inset-0 bg-gradient-to-br from-purple-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div class="relative flex items-start">
              <span class="text-2xl mr-4 group-hover:scale-125 transition-all duration-300 filter group-hover:brightness-110">{{ prompt.emoji }}</span>
              <div class="flex-1">
                <div class="font-semibold text-gray-800 dark:text-gray-200 mb-1 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-200">{{ prompt.title }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{{ prompt.description }}</div>
              </div>
              <!-- 右侧装饰图标 -->
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 ml-2">
                <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </button>
        </div>
      </div>

      <!-- 学生管理类 -->
      <div class="category-section mb-6">
        <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3 flex items-center">
          <span class="text-lg mr-2">👥</span>
          学生管理
        </h3>
        <div class="space-y-3">
          <button
            v-for="prompt in studentManagementPrompts"
            :key="prompt.text"
            @click="$emit('use-prompt', prompt.text)"
            class="relative w-full text-left p-4 bg-gradient-to-br from-orange-50 via-orange-100/80 to-orange-150/60 dark:from-orange-900/30 dark:via-orange-800/25 dark:to-orange-700/20 rounded-xl border border-orange-200/60 dark:border-orange-600/40 hover:border-orange-300/80 dark:hover:border-orange-500/60 shadow-sm hover:shadow-lg hover:shadow-orange-500/20 dark:hover:shadow-orange-600/30 transition-all duration-300 group hover:scale-[1.02] hover:-translate-y-0.5 overflow-hidden backdrop-blur-sm"
          >
            <!-- 微妙的背景光晕 -->
            <div class="absolute inset-0 bg-gradient-to-br from-orange-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div class="relative flex items-start">
              <span class="text-2xl mr-4 group-hover:scale-125 transition-all duration-300 filter group-hover:brightness-110">{{ prompt.emoji }}</span>
              <div class="flex-1">
                <div class="font-semibold text-gray-800 dark:text-gray-200 mb-1 group-hover:text-orange-700 dark:group-hover:text-orange-300 transition-colors duration-200">{{ prompt.title }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{{ prompt.description }}</div>
              </div>
              <!-- 右侧装饰图标 -->
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 ml-2">
                <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </button>
        </div>
      </div>

      <!-- 教学工具类 -->
      <div class="category-section mb-6">
        <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3 flex items-center">
          <span class="text-lg mr-2">🛠️</span>
          教学工具
        </h3>
        <div class="space-y-3">
          <button
            v-for="prompt in teachingToolsPrompts"
            :key="prompt.text"
            @click="$emit('use-prompt', prompt.text)"
            class="relative w-full text-left p-4 bg-gradient-to-br from-teal-50 via-teal-100/80 to-teal-150/60 dark:from-teal-900/30 dark:via-teal-800/25 dark:to-teal-700/20 rounded-xl border border-teal-200/60 dark:border-teal-600/40 hover:border-teal-300/80 dark:hover:border-teal-500/60 shadow-sm hover:shadow-lg hover:shadow-teal-500/20 dark:hover:shadow-teal-600/30 transition-all duration-300 group hover:scale-[1.02] hover:-translate-y-0.5 overflow-hidden backdrop-blur-sm"
          >
            <!-- 微妙的背景光晕 -->
            <div class="absolute inset-0 bg-gradient-to-br from-teal-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div class="relative flex items-start">
              <span class="text-2xl mr-4 group-hover:scale-125 transition-all duration-300 filter group-hover:brightness-110">{{ prompt.emoji }}</span>
              <div class="flex-1">
                <div class="font-semibold text-gray-800 dark:text-gray-200 mb-1 group-hover:text-teal-700 dark:group-hover:text-teal-300 transition-colors duration-200">{{ prompt.title }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{{ prompt.description }}</div>
              </div>
              <!-- 右侧装饰图标 -->
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 ml-2">
                <svg class="w-4 h-4 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </button>
        </div>
      </div>

      <!-- 专业发展类 -->
      <div class="category-section">
        <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3 flex items-center">
          <span class="text-lg mr-2">🌟</span>
          专业发展
        </h3>
        <div class="space-y-3">
          <button
            v-for="prompt in professionalDevelopmentPrompts"
            :key="prompt.text"
            @click="$emit('use-prompt', prompt.text)"
            class="relative w-full text-left p-4 bg-gradient-to-br from-indigo-50 via-indigo-100/80 to-indigo-150/60 dark:from-indigo-900/30 dark:via-indigo-800/25 dark:to-indigo-700/20 rounded-xl border border-indigo-200/60 dark:border-indigo-600/40 hover:border-indigo-300/80 dark:hover:border-indigo-500/60 shadow-sm hover:shadow-lg hover:shadow-indigo-500/20 dark:hover:shadow-indigo-600/30 transition-all duration-300 group hover:scale-[1.02] hover:-translate-y-0.5 overflow-hidden backdrop-blur-sm"
          >
            <!-- 微妙的背景光晕 -->
            <div class="absolute inset-0 bg-gradient-to-br from-indigo-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div class="relative flex items-start">
              <span class="text-2xl mr-4 group-hover:scale-125 transition-all duration-300 filter group-hover:brightness-110">{{ prompt.emoji }}</span>
              <div class="flex-1">
                <div class="font-semibold text-gray-800 dark:text-gray-200 mb-1 group-hover:text-indigo-700 dark:group-hover:text-indigo-300 transition-colors duration-200">{{ prompt.title }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{{ prompt.description }}</div>
              </div>
              <!-- 右侧装饰图标 -->
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 ml-2">
                <svg class="w-4 h-4 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 美化的底部提示区域 -->
    <div class="sidebar-footer relative overflow-hidden">
      <!-- 底部渐变背景 -->
      <div class="absolute inset-0 bg-gradient-to-r from-gray-50/80 via-blue-50/40 to-indigo-50/30 dark:from-gray-800/90 dark:via-gray-700/60 dark:to-gray-800/80"></div>

      <!-- 装饰性元素 -->
      <div class="absolute inset-0 pointer-events-none">
        <div class="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-200 to-transparent dark:via-blue-600/30"></div>
      </div>

      <!-- 主要内容 -->
      <div class="relative z-10 p-4 border-t border-white/40 dark:border-gray-600/40">
        <div class="flex items-center justify-center text-sm text-gray-600 dark:text-gray-300 space-x-2">
          <!-- 美化的提示图标 -->
          <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/40 dark:to-indigo-900/40 rounded-full flex items-center justify-center shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>

          <!-- 提示文字 -->
          <div class="text-center">
            <p class="font-medium text-gray-700 dark:text-gray-200">
              点击任意模板开始您的
              <span class="text-blue-600 dark:text-blue-400 font-semibold">AI教学之旅</span>
            </p>
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 opacity-80">
              智能助手将为您提供专业指导
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 基础教学类提示
const basicTeachingPrompts = [
  {
    emoji: '💬',
    title: '教学咨询',
    description: '获取专业的教学方法和策略建议',
    text: '我想了解如何提高小学生的学习兴趣，让课堂更加生动有趣'
  },
  {
    emoji: '📚',
    title: '学习指导',
    description: '为学生提供个性化的学习建议',
    text: '请帮我分析如何针对不同学习能力的学生制定差异化教学策略'
  },
  {
    emoji: '🎯',
    title: '课堂管理',
    description: '改善课堂纪律和学生参与度',
    text: '如何有效管理课堂纪律，提高学生的课堂参与度和注意力'
  }
];

// 课程设计类提示
const courseDesignPrompts = [
  {
    emoji: '📋',
    title: '教案制作',
    description: '创建详细的教学计划和教案',
    text: '请帮我制作一份小学数学"分数的认识"的详细教案，包括教学目标、重难点、教学过程等'
  },
  {
    emoji: '🎨',
    title: '课程规划',
    description: '设计完整的课程体系和进度',
    text: '我需要为初中英语设计一个学期的课程规划，请帮我制定教学计划和进度安排'
  },
  {
    emoji: '🎯',
    title: '教学目标',
    description: '制定清晰的教学目标和评价标准',
    text: '请帮我为高中物理"电磁感应"这一章制定具体的教学目标和学习成果评价标准'
  },
  {
    emoji: '📊',
    title: '学情分析',
    description: '深入分析学生学习情况和需求',
    text: '如何对班级学生进行有效的学情分析，了解他们的学习基础和个性特点'
  }
];

// 教学方法类提示
const teachingMethodPrompts = [
  {
    emoji: '🎪',
    title: '互动教学',
    description: '增强师生互动，活跃课堂氛围',
    text: '请推荐一些适合中学历史课的互动教学方法，能够让学生积极参与课堂讨论'
  },
  {
    emoji: '🎬',
    title: '情景教学',
    description: '创设生动的教学情境',
    text: '如何在小学语文课堂中运用情景教学法，让学生更好地理解课文内容'
  },
  {
    emoji: '🔬',
    title: '探究式学习',
    description: '培养学生的探索和研究能力',
    text: '请设计一个科学探究活动，帮助学生理解化学反应的基本原理'
  },
  {
    emoji: '👥',
    title: '合作学习',
    description: '促进学生团队协作和互助',
    text: '如何在数学课堂中有效组织小组合作学习，提高学生的解题能力'
  }
];

// 学生管理类提示
const studentManagementPrompts = [
  {
    emoji: '📈',
    title: '学习评估',
    description: '科学评价学生的学习表现',
    text: '请帮我设计一套多元化的学生学习评价体系，包括过程性评价和结果性评价'
  },
  {
    emoji: '💡',
    title: '个性化指导',
    description: '针对不同学生特点进行指导',
    text: '班级中有学习困难的学生，请提供一些个性化的教学指导策略'
  },
  {
    emoji: '🏆',
    title: '激励机制',
    description: '建立有效的学生激励体系',
    text: '如何设计课堂激励机制，激发学生的学习积极性和主动性'
  },
  {
    emoji: '🤝',
    title: '家校沟通',
    description: '加强与家长的有效沟通',
    text: '请提供一些与家长有效沟通的技巧和方法，促进家校合作'
  }
];

// 教学工具类提示
const teachingToolsPrompts = [
  {
    emoji: '💻',
    title: '课件制作',
    description: '制作精美实用的教学课件',
    text: '请指导我如何制作一个关于"光的反射"的物理课件，需要包含哪些元素'
  },
  {
    emoji: '📱',
    title: '数字化教学',
    description: '运用现代技术辅助教学',
    text: '如何在课堂教学中有效运用多媒体和数字化工具，提升教学效果'
  },
  {
    emoji: '🔬',
    title: '实验设计',
    description: '设计安全有效的教学实验',
    text: '请帮我设计一个简单安全的化学实验，演示酸碱反应的过程'
  },
  {
    emoji: '📝',
    title: '练习题库',
    description: '构建系统的练习题目体系',
    text: '请为高中数学"函数"单元设计一套分层次的练习题，包括基础、提高、拓展三个层次'
  }
];

// 专业发展类提示
const professionalDevelopmentPrompts = [
  {
    emoji: '🌟',
    title: '教学反思',
    description: '深入反思教学过程和效果',
    text: '请帮我分析这节课的教学效果，找出优点和不足，并提供改进建议'
  },
  {
    emoji: '📖',
    title: '教育理论',
    description: '学习先进的教育理念和理论',
    text: '请介绍一些现代教育理论，如建构主义、多元智能理论等在实际教学中的应用'
  },
  {
    emoji: '🎓',
    title: '专业成长',
    description: '规划教师职业发展路径',
    text: '作为一名新教师，请为我制定一个专业成长计划，包括短期和长期目标'
  },
  {
    emoji: '🔄',
    title: '教学创新',
    description: '探索新的教学方法和模式',
    text: '请分享一些创新的教学方法和理念，帮助我突破传统教学模式'
  }
];

// 定义事件
defineEmits(['use-prompt']);
</script>

<style scoped>
.quick-start-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
}

.feature-item {
  transition: all 0.2s ease;
}

.feature-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* 深色模式滚动条 */
.dark .sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3);
}

.dark .sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.5);
}

/* 增强的动画效果 */
@keyframes gentle-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes soft-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
}

/* 按钮悬浮增强效果 */
.category-section button:hover {
  animation: gentle-float 2s ease-in-out infinite;
}

/* 头部图标动画 */
.sidebar-header .w-12:hover {
  animation: soft-glow 2s ease-in-out infinite;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .quick-start-sidebar {
    border-radius: 0.75rem;
  }

  .sidebar-header {
    padding: 1rem;
  }

  .sidebar-content {
    padding: 1rem;
  }

  .category-section button {
    padding: 0.75rem;
  }
}

/* 深色模式增强 */
@media (prefers-color-scheme: dark) {
  .quick-start-sidebar {
    box-shadow:
      0 10px 25px -5px rgba(0, 0, 0, 0.3),
      0 4px 6px -2px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }
}
</style>
