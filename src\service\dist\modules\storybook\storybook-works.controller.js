"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookWorksController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const storybook_works_service_1 = require("./storybook-works.service");
const storybook_export_service_1 = require("./storybook-export.service");
const dto_1 = require("./dto");
let StorybookWorksController = class StorybookWorksController {
    constructor(storybookWorksService, storybookExportService) {
        this.storybookWorksService = storybookWorksService;
        this.storybookExportService = storybookExportService;
    }
    async createFolder(createFolderDto, req) {
        const userId = req.user['id'];
        return this.storybookWorksService.createFolder(userId, createFolderDto);
    }
    async getUserFolders(queryFolderDto, req) {
        const userId = req.user['id'];
        return this.storybookWorksService.getUserFolders(userId, queryFolderDto);
    }
    async getFolderDetail(id, req) {
        const userId = req.user['id'];
        return this.storybookWorksService.getFolderDetail(id, userId);
    }
    async updateFolder(id, updateFolderDto, req) {
        const userId = req.user['id'];
        return this.storybookWorksService.updateFolder(id, userId, updateFolderDto);
    }
    async deleteFolder(id, req) {
        const userId = req.user['id'];
        await this.storybookWorksService.deleteFolder(id, userId);
        return { success: true, message: '文件夹删除成功' };
    }
    async createStorybook(createStorybookDto, req) {
        const userId = req.user['id'];
        return this.storybookWorksService.createStorybook(userId, createStorybookDto);
    }
    async getStorybookDetail(id, req) {
        const userId = req.user['id'];
        return this.storybookWorksService.getStorybookDetail(id, userId);
    }
    async updateStorybook(id, updateStorybookDto, req) {
        const userId = req.user['id'];
        return this.storybookWorksService.updateStorybook(id, userId, updateStorybookDto);
    }
    async getUserStorybooks(queryStorybookDto, req) {
        const userId = req.user['id'];
        return this.storybookWorksService.getUserStorybooks(userId, queryStorybookDto);
    }
    async batchOperation(batchOperationDto, req) {
        const userId = req.user['id'];
        await this.storybookWorksService.batchOperation(userId, batchOperationDto);
        const operationMessages = {
            'delete': '作品已移至回收站',
            'restore': '作品已从回收站恢复',
            'move': '作品已移动到指定文件夹'
        };
        return {
            success: true,
            message: operationMessages[batchOperationDto.operation] || '操作成功'
        };
    }
    async permanentlyDeleteStorybook(id, req) {
        const userId = req.user['id'];
        await this.storybookWorksService.permanentlyDeleteStorybook(id, userId);
        return { success: true, message: '作品已永久删除' };
    }
    async emptyTrash(req) {
        const userId = req.user['id'];
        await this.storybookWorksService.emptyTrash(userId);
        return { success: true, message: '回收站已清空' };
    }
    async exportToPdf(id, options, req) {
        const userId = req.user['id'];
        const result = await this.storybookExportService.exportToPdf(id, userId, options);
        return {
            success: true,
            message: '绘本导出成功',
            data: result
        };
    }
    async exportToImages(id, options, req) {
        const userId = req.user['id'];
        const result = await this.storybookExportService.exportToImages(id, userId, options);
        return {
            success: true,
            message: '绘本导出成功',
            data: result
        };
    }
};
__decorate([
    (0, common_1.Post)('folder'),
    (0, swagger_1.ApiOperation)({ summary: '创建文件夹' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateFolderDto, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "createFolder", null);
__decorate([
    (0, common_1.Get)('folder'),
    (0, swagger_1.ApiOperation)({ summary: '获取文件夹列表' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.QueryFolderDto, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "getUserFolders", null);
__decorate([
    (0, common_1.Get)('folder/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取文件夹详情' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "getFolderDetail", null);
__decorate([
    (0, common_1.Put)('folder/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新文件夹' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateFolderDto, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "updateFolder", null);
__decorate([
    (0, common_1.Delete)('folder/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除文件夹' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "deleteFolder", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建绘本' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateStorybookDto, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "createStorybook", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取绘本详情' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "getStorybookDetail", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新绘本' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateStorybookDto, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "updateStorybook", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取作品列表' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.QueryStorybookDto, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "getUserStorybooks", null);
__decorate([
    (0, common_1.Post)('batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量操作作品' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BatchOperationDto, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "batchOperation", null);
__decorate([
    (0, common_1.Delete)('permanent/:id'),
    (0, swagger_1.ApiOperation)({ summary: '永久删除作品' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "permanentlyDeleteStorybook", null);
__decorate([
    (0, common_1.Delete)('trash'),
    (0, swagger_1.ApiOperation)({ summary: '清空回收站' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "emptyTrash", null);
__decorate([
    (0, common_1.Post)(':id/export/pdf'),
    (0, swagger_1.ApiOperation)({ summary: '导出绘本为PDF' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "exportToPdf", null);
__decorate([
    (0, common_1.Post)(':id/export/images'),
    (0, swagger_1.ApiOperation)({ summary: '导出绘本为图片集' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], StorybookWorksController.prototype, "exportToImages", null);
StorybookWorksController = __decorate([
    (0, common_1.Controller)('storybook/works'),
    (0, swagger_1.ApiTags)('绘本作品管理'),
    __metadata("design:paramtypes", [storybook_works_service_1.StorybookWorksService,
        storybook_export_service_1.StorybookExportService])
], StorybookWorksController);
exports.StorybookWorksController = StorybookWorksController;
