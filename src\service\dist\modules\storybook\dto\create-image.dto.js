"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateImageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateImageDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片URL' }),
    (0, class_validator_1.IsNotEmpty)({ message: '图片URL不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateImageDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateImageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成提示词', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateImageDto.prototype, "prompt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用的模型', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateImageDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片类型(1:角色图,2:页面图,3:封面图)', required: false, default: 2 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateImageDto.prototype, "imageType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属绘本ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateImageDto.prototype, "storybookId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属页面ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateImageDto.prototype, "pageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属角色ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateImageDto.prototype, "characterId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片宽度', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateImageDto.prototype, "width", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片高度', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateImageDto.prototype, "height", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片大小(KB)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateImageDto.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片格式', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateImageDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成参数', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateImageDto.prototype, "generationParams", void 0);
exports.CreateImageDto = CreateImageDto;
