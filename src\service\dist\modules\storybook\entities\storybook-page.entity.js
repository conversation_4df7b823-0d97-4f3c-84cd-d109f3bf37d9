"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookPageEntity = void 0;
const baseEntity_1 = require("../../../common/entity/baseEntity");
const typeorm_1 = require("typeorm");
const storybook_entity_1 = require("./storybook.entity");
let StorybookPageEntity = class StorybookPageEntity extends baseEntity_1.BaseEntity {
};
__decorate([
    (0, typeorm_1.Column)({ comment: '所属绘本ID' }),
    __metadata("design:type", Number)
], StorybookPageEntity.prototype, "storybookId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '页码' }),
    __metadata("design:type", Number)
], StorybookPageEntity.prototype, "pageNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '页面文本内容', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookPageEntity.prototype, "text", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '页面图片URL', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookPageEntity.prototype, "imageUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '布局类型', default: 'vertical' }),
    __metadata("design:type", String)
], StorybookPageEntity.prototype, "layout", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '文本样式配置', type: 'json', nullable: true }),
    __metadata("design:type", Object)
], StorybookPageEntity.prototype, "textStyle", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '场景元素', type: 'json', nullable: true }),
    __metadata("design:type", Object)
], StorybookPageEntity.prototype, "sceneElements", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '画面描述', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookPageEntity.prototype, "imageDescription", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => storybook_entity_1.StorybookEntity, storybook => storybook.pages, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'storybookId' }),
    __metadata("design:type", storybook_entity_1.StorybookEntity)
], StorybookPageEntity.prototype, "storybook", void 0);
StorybookPageEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'storybook_page' })
], StorybookPageEntity);
exports.StorybookPageEntity = StorybookPageEntity;
