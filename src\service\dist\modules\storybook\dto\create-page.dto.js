"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePageDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属绘本ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '绘本ID不能为空' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePageDto.prototype, "storybookId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码' }),
    (0, class_validator_1.IsNotEmpty)({ message: '页码不能为空' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePageDto.prototype, "pageNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页面文本内容', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePageDto.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页面图片URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePageDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '布局类型', required: false, default: 'vertical' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePageDto.prototype, "layout", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文本样式配置', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreatePageDto.prototype, "textStyle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '场景元素', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreatePageDto.prototype, "sceneElements", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '画面描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePageDto.prototype, "imageDescription", void 0);
exports.CreatePageDto = CreatePageDto;
