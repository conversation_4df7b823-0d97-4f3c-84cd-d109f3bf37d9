"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwtAuth_guard_1 = require("../../common/auth/jwtAuth.guard");
const storybook_service_1 = require("./storybook.service");
const dto_1 = require("./dto");
const create_image_dto_1 = require("./dto/create-image.dto");
const update_image_dto_1 = require("./dto/update-image.dto");
let StorybookController = class StorybookController {
    constructor(storybookService) {
        this.storybookService = storybookService;
    }
    async createPage(createPageDto) {
        return this.storybookService.createPage(createPageDto);
    }
    async updatePage(id, updateData) {
        return this.storybookService.updatePage(id, updateData);
    }
    async deletePage(id) {
        await this.storybookService.deletePage(id);
        return { success: true, message: '删除成功' };
    }
    async getStorybookPages(id) {
        return this.storybookService.getStorybookPages(id);
    }
    async createCharacter(createCharacterDto) {
        return this.storybookService.createCharacter(createCharacterDto);
    }
    async updateCharacter(id, updateData) {
        return this.storybookService.updateCharacter(id, updateData);
    }
    async deleteCharacter(id) {
        await this.storybookService.deleteCharacter(id);
        return { success: true, message: '删除成功' };
    }
    async getStorybookCharacters(id) {
        return this.storybookService.getStorybookCharacters(id);
    }
    async getCharacterTemplates() {
        return this.storybookService.getCharacterTemplates();
    }
    async getUserCharacters(req) {
        const userId = req.user['id'];
        return this.storybookService.getUserCharacters(userId);
    }
    async getConfig(key) {
        const config = await this.storybookService.getConfig(key);
        if (!config || config.public !== 1) {
            return { configKey: key, configVal: '', public: 0 };
        }
        return config;
    }
    async getFoxAssistantConfig() {
        const configs = await this.storybookService.getAllConfigs();
        const foxAssistantConfig = {
            foxAssistantModel: this.getConfigValue(configs, 'foxAssistantModel') || this.getConfigValue(configs, 'aiModel') || 'deepseek-v3',
            foxAssistantTemperature: parseFloat(this.getConfigValue(configs, 'foxAssistantTemperature') || this.getConfigValue(configs, 'temperature') || '0.7'),
            foxAssistantMaxTokens: parseInt(this.getConfigValue(configs, 'foxAssistantMaxTokens') || this.getConfigValue(configs, 'maxTokens') || '2000'),
            foxAssistantSystemPrompt: this.getConfigValue(configs, 'foxAssistantSystemPrompt') || this.getConfigValue(configs, 'systemPrompt') ||
                '你是一个专业的儿童绘本创作助手，擅长创作有教育意义、富有想象力的儿童故事，并能以简单易懂的方式回答儿童关于创作的问题。',
            aiModel: this.getConfigValue(configs, 'aiModel') || 'deepseek-v3',
            temperature: parseFloat(this.getConfigValue(configs, 'temperature') || '0.7'),
            maxTokens: parseInt(this.getConfigValue(configs, 'maxTokens') || '2000'),
            systemPrompt: this.getConfigValue(configs, 'systemPrompt') ||
                '你是一个专业的儿童绘本创作助手，擅长创作有教育意义、富有想象力的儿童故事。',
        };
        return foxAssistantConfig;
    }
    getConfigValue(configs, key) {
        const config = configs.find(c => c.configKey === key);
        return config ? config.configVal : null;
    }
    async createImage(createImageDto, req) {
        const userId = req.user['id'];
        return this.storybookService.createImage(userId, createImageDto);
    }
    async getImages(req, page, limit, imageType, storybookId, pageId, characterId, keyword) {
        const userId = req.user['id'];
        return this.storybookService.getImages({
            page,
            limit,
            userId,
            imageType,
            storybookId,
            pageId,
            characterId,
            keyword
        });
    }
    async getImageDetail(id) {
        return this.storybookService.getImageDetail(id);
    }
    async updateImage(id, updateImageDto) {
        return this.storybookService.updateImage(id, updateImageDto);
    }
    async deleteImage(id) {
        await this.storybookService.deleteImage(id);
        return { success: true, message: '删除成功' };
    }
    async getImageGenerationConfig() {
        return this.storybookService.getImageGenerationConfig();
    }
    async getImageGenerationConfigWithId(id) {
        return this.storybookService.getImageGenerationConfig();
    }
    async filterSensitiveContent(content, req) {
        const userId = req.user['id'];
        const filteredContent = await this.storybookService.filterSensitiveContent(content, userId);
        return { original: content, filtered: filteredContent };
    }
    async checkContent(content, req) {
        const userId = req.user['id'];
        return this.storybookService.checkContent(content, userId);
    }
};
__decorate([
    (0, common_1.Post)('page'),
    (0, swagger_1.ApiOperation)({ summary: '创建绘本页面' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreatePageDto]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "createPage", null);
__decorate([
    (0, common_1.Put)('page/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新绘本页面' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '页面ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "updatePage", null);
__decorate([
    (0, common_1.Delete)('page/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除绘本页面' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '页面ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "deletePage", null);
__decorate([
    (0, common_1.Get)(':id/pages'),
    (0, swagger_1.ApiOperation)({ summary: '获取绘本的所有页面' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '绘本ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getStorybookPages", null);
__decorate([
    (0, common_1.Post)('character'),
    (0, swagger_1.ApiOperation)({ summary: '创建角色' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateCharacterDto]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "createCharacter", null);
__decorate([
    (0, common_1.Put)('character/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新角色' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '角色ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "updateCharacter", null);
__decorate([
    (0, common_1.Delete)('character/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除角色' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '角色ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "deleteCharacter", null);
__decorate([
    (0, common_1.Get)(':id/characters'),
    (0, swagger_1.ApiOperation)({ summary: '获取绘本的所有角色' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '绘本ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getStorybookCharacters", null);
__decorate([
    (0, common_1.Get)('character/templates'),
    (0, swagger_1.ApiOperation)({ summary: '获取角色模板' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getCharacterTemplates", null);
__decorate([
    (0, common_1.Get)('character/mine'),
    (0, swagger_1.ApiOperation)({ summary: '获取我的角色库' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getUserCharacters", null);
__decorate([
    (0, common_1.Get)('config/:key'),
    (0, swagger_1.ApiOperation)({ summary: '获取配置' }),
    (0, swagger_1.ApiParam)({ name: 'key', description: '配置键' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getConfig", null);
__decorate([
    (0, common_1.Get)('fox-assistant-config'),
    (0, swagger_1.ApiOperation)({ summary: '获取小狐狸助手配置' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getFoxAssistantConfig", null);
__decorate([
    (0, common_1.Post)('image'),
    (0, swagger_1.ApiOperation)({ summary: '创建图像记录' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_image_dto_1.CreateImageDto, Object]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "createImage", null);
__decorate([
    (0, common_1.Get)('image'),
    (0, swagger_1.ApiOperation)({ summary: '获取图像列表' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量' }),
    (0, swagger_1.ApiQuery)({ name: 'imageType', required: false, description: '图像类型' }),
    (0, swagger_1.ApiQuery)({ name: 'storybookId', required: false, description: '绘本ID' }),
    (0, swagger_1.ApiQuery)({ name: 'pageId', required: false, description: '页面ID' }),
    (0, swagger_1.ApiQuery)({ name: 'characterId', required: false, description: '角色ID' }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', required: false, description: '关键词' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('imageType')),
    __param(4, (0, common_1.Query)('storybookId')),
    __param(5, (0, common_1.Query)('pageId')),
    __param(6, (0, common_1.Query)('characterId')),
    __param(7, (0, common_1.Query)('keyword')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number, Number, Number, Number, Number, String]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getImages", null);
__decorate([
    (0, common_1.Get)('image/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取图像详情' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '图像ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getImageDetail", null);
__decorate([
    (0, common_1.Put)('image/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新图像' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '图像ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_image_dto_1.UpdateImageDto]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "updateImage", null);
__decorate([
    (0, common_1.Delete)('image/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除图像' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiParam)({ name: 'id', description: '图像ID' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "deleteImage", null);
__decorate([
    (0, common_1.Get)('image-config'),
    (0, swagger_1.ApiOperation)({ summary: '获取图像生成配置' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getImageGenerationConfig", null);
__decorate([
    (0, common_1.Get)('image-config/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取图像生成配置（带ID参数）' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "getImageGenerationConfigWithId", null);
__decorate([
    (0, common_1.Post)('filter-content'),
    (0, swagger_1.ApiOperation)({ summary: '过滤内容中的敏感词' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)('content')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "filterSensitiveContent", null);
__decorate([
    (0, common_1.Post)('check-content'),
    (0, swagger_1.ApiOperation)({ summary: '检查内容是否包含敏感词' }),
    (0, common_1.UseGuards)(jwtAuth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)('content')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], StorybookController.prototype, "checkContent", null);
StorybookController = __decorate([
    (0, swagger_1.ApiTags)('Storybook'),
    (0, common_1.Controller)('storybook'),
    __metadata("design:paramtypes", [storybook_service_1.StorybookService])
], StorybookController);
exports.StorybookController = StorybookController;
