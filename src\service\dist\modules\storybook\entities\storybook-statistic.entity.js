"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookStatisticEntity = void 0;
const baseEntity_1 = require("../../../common/entity/baseEntity");
const typeorm_1 = require("typeorm");
let StorybookStatisticEntity = class StorybookStatisticEntity extends baseEntity_1.BaseEntity {
};
__decorate([
    (0, typeorm_1.Column)({ comment: '统计日期', type: 'date' }),
    __metadata("design:type", Date)
], StorybookStatisticEntity.prototype, "date", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '新增绘本数', default: 0 }),
    __metadata("design:type", Number)
], StorybookStatisticEntity.prototype, "newStorybookCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '总浏览量', default: 0 }),
    __metadata("design:type", Number)
], StorybookStatisticEntity.prototype, "totalViewCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '总点赞量', default: 0 }),
    __metadata("design:type", Number)
], StorybookStatisticEntity.prototype, "totalLikeCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活跃用户数', default: 0 }),
    __metadata("design:type", Number)
], StorybookStatisticEntity.prototype, "activeUserCount", void 0);
StorybookStatisticEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'storybook_statistic' })
], StorybookStatisticEntity);
exports.StorybookStatisticEntity = StorybookStatisticEntity;
