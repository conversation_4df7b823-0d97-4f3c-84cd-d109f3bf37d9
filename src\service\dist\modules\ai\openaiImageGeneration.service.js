"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OpenAIImageGenerationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIImageGenerationService = void 0;
const common_1 = require("@nestjs/common");
const globalConfig_service_1 = require("../../modules/globalConfig/globalConfig.service");
const upload_service_1 = require("../../modules/upload/upload.service");
const chatLog_service_1 = require("../../modules/chatLog/chatLog.service");
const openaiChat_service_1 = require("./openaiChat.service");
const axios_1 = require("axios");
let OpenAIImageGenerationService = OpenAIImageGenerationService_1 = class OpenAIImageGenerationService {
    constructor(globalConfigService, uploadService, chatLogService, openAIChatService) {
        this.globalConfigService = globalConfigService;
        this.uploadService = uploadService;
        this.chatLogService = chatLogService;
        this.openAIChatService = openAIChatService;
        this.logger = new common_1.Logger(OpenAIImageGenerationService_1.name);
    }
    async generateImage(inputs, buildMessageFromParentMessageId) {
        var _a, _b, _c, _d;
        common_1.Logger.log('开始提交 OpenAI 图像生成任务', 'OpenAIImageGenerationService');
        const { apiKey, model, proxyUrl, prompt, extraParam, timeout, onSuccess, onFailure, groupId, assistantLogId, } = inputs;
        const { isImageGenChat, isConvertToBase64 } = await this.globalConfigService.getConfigs([
            'isImageGenChat',
            'isConvertToBase64',
        ]);
        let imagePrompt;
        if (isImageGenChat === '1' && buildMessageFromParentMessageId) {
            try {
                common_1.Logger.log('已开启连续对话模式，使用上下文优化提示词', 'OpenAIImageGenerationService');
                const { messagesHistory } = await buildMessageFromParentMessageId(`参考上文，结合我的需求，给出详细的图像描述。我的需求是：${prompt}`, {
                    groupId,
                    systemMessage: '你是一个图像提示词生成工具，请根据用户的要求，结合上下文，用一段详细的文字描述用户需要的图像。不要包含任何礼貌性的寒暄，只需要场景的描述，可以适当联想细节以丰富图像效果。',
                    maxModelTokens: 8000,
                    maxRounds: 5,
                    fileInfo: '',
                }, this.chatLogService);
                imagePrompt = await this.openAIChatService.chatFree(prompt, undefined, messagesHistory);
            }
            catch (error) {
                common_1.Logger.error('调用chatFree优化提示词失败：', error);
                imagePrompt = prompt;
            }
        }
        else {
            imagePrompt = prompt;
        }
        const size = (extraParam === null || extraParam === void 0 ? void 0 : extraParam.size) || '1024x1024';
        const quality = (extraParam === null || extraParam === void 0 ? void 0 : extraParam.quality) || 'standard';
        const style = (extraParam === null || extraParam === void 0 ? void 0 : extraParam.style) || 'vivid';
        const n = (extraParam === null || extraParam === void 0 ? void 0 : extraParam.n) || 1;
        const responseFormat = isConvertToBase64 === '1' ? 'b64_json' : 'url';
        let result = {
            answer: '',
            fileInfo: '',
            status: 2,
            drawId: '',
            customId: '',
            progress: '0%'
        };
        if (assistantLogId) {
            await this.chatLogService.updateChatLog(assistantLogId, {
                answer: '图像生成中...',
                progress: '10%',
                status: 2,
            });
        }
        try {
            const options = {
                method: 'POST',
                url: `${proxyUrl}/v1/images/generations`,
                timeout: timeout,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${apiKey}`,
                },
                data: {
                    model: model,
                    prompt: imagePrompt,
                    n: n,
                    size: size,
                    quality: quality,
                    style: style,
                    response_format: responseFormat,
                },
            };
            common_1.Logger.log(`正在准备发送请求到 ${options.url}，payload: ${JSON.stringify(options.data)}, headers: ${JSON.stringify(options.headers)}`, 'OpenAIImageGenerationService');
            if (assistantLogId) {
                await this.chatLogService.updateChatLog(assistantLogId, {
                    answer: '正在生成图像...',
                    progress: '30%',
                    status: 2,
                });
            }
            const response = await (0, axios_1.default)(options);
            if (assistantLogId) {
                await this.chatLogService.updateChatLog(assistantLogId, {
                    answer: '图像生成完成，正在处理结果...',
                    progress: '70%',
                    status: 2,
                });
            }
            const images = response.data.data;
            const imageUrls = [];
            for (let i = 0; i < images.length; i++) {
                const image = images[i];
                let imageUrl;
                if (responseFormat === 'b64_json') {
                    const base64Data = image.b64_json;
                    const buffer = Buffer.from(base64Data, 'base64');
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const day = String(now.getDate()).padStart(2, '0');
                    const currentDate = `${year}${month}/${day}`;
                    imageUrl = await this.uploadService.uploadBuffer({
                        buffer,
                        filename: `openai_image_${Date.now()}_${i}.png`,
                        dir: `images/openai/${currentDate}`,
                    });
                }
                else {
                    try {
                        common_1.Logger.log(`开始上传图片 ${i + 1}/${images.length}`, 'OpenAIImageGenerationService');
                        const now = new Date();
                        const year = now.getFullYear();
                        const month = String(now.getMonth() + 1).padStart(2, '0');
                        const day = String(now.getDate()).padStart(2, '0');
                        const currentDate = `${year}${month}/${day}`;
                        imageUrl = await this.uploadService.uploadFileFromUrl({
                            url: image.url,
                            dir: `images/openai/${currentDate}`,
                        });
                        common_1.Logger.log(`图片 ${i + 1} 上传成功，URL: ${imageUrl}`, 'OpenAIImageGenerationService');
                    }
                    catch (error) {
                        common_1.Logger.error(`上传图片 ${i + 1} 过程中出现错误: ${error}`, 'OpenAIImageGenerationService');
                        imageUrl = image.url;
                    }
                }
                imageUrls.push(imageUrl);
            }
            let replyText;
            try {
                replyText = await this.openAIChatService.chatFree(`根据提示词"${imagePrompt}"，我已经生成了${images.length}张图像。请用中文回复，告诉用户图像已经生成完成，并简要描述图像内容。`);
            }
            catch (error) {
                replyText = `已根据您的提示"${prompt}"生成${images.length}张图像`;
                common_1.Logger.error('生成回复文本失败: ', error);
            }
            result.answer = replyText;
            result.fileInfo = imageUrls[0];
            result.status = 3;
            result.customId = JSON.stringify(imageUrls);
            result.progress = '100%';
            if (assistantLogId) {
                await this.chatLogService.updateChatLog(assistantLogId, {
                    fileInfo: result.fileInfo,
                    answer: result.answer,
                    progress: result.progress,
                    status: result.status,
                    customId: result.customId,
                });
            }
            if (onSuccess) {
                onSuccess(result);
            }
            return result;
        }
        catch (error) {
            result.status = 5;
            result.progress = '0%';
            const status = ((_a = error === null || error === void 0 ? void 0 : error.response) === null || _a === void 0 ? void 0 : _a.status) || 500;
            const message = ((_d = (_c = (_b = error === null || error === void 0 ? void 0 : error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.error) === null || _d === void 0 ? void 0 : _d.message) || '未知错误';
            common_1.Logger.error(`图像生成失败: ${status} - ${message}`, error, 'OpenAIImageGenerationService');
            if (status === 429) {
                result.answer = '当前请求已过载，请稍后再试！';
            }
            else if (status === 400) {
                result.answer = `请求参数错误: ${message}`;
            }
            else if (status === 401) {
                result.answer = 'API密钥无效或已过期';
            }
            else {
                result.answer = `图像生成失败: ${message}`;
            }
            if (assistantLogId) {
                await this.chatLogService.updateChatLog(assistantLogId, {
                    answer: result.answer,
                    status: result.status,
                });
            }
            if (onFailure) {
                onFailure(result);
            }
            return result;
        }
    }
};
OpenAIImageGenerationService = OpenAIImageGenerationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [globalConfig_service_1.GlobalConfigService,
        upload_service_1.UploadService,
        chatLog_service_1.ChatLogService,
        openaiChat_service_1.OpenAIChatService])
], OpenAIImageGenerationService);
exports.OpenAIImageGenerationService = OpenAIImageGenerationService;
