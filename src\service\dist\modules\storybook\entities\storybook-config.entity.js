"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookConfigEntity = void 0;
const baseEntity_1 = require("../../../common/entity/baseEntity");
const typeorm_1 = require("typeorm");
let StorybookConfigEntity = class StorybookConfigEntity extends baseEntity_1.BaseEntity {
};
__decorate([
    (0, typeorm_1.Column)({ comment: '配置键', unique: true }),
    __metadata("design:type", String)
], StorybookConfigEntity.prototype, "configKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '配置值', type: 'text' }),
    __metadata("design:type", String)
], StorybookConfigEntity.prototype, "configVal", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '配置描述', nullable: true }),
    __metadata("design:type", String)
], StorybookConfigEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否公开(0:私有,1:公开)', default: 0 }),
    __metadata("design:type", Number)
], StorybookConfigEntity.prototype, "public", void 0);
StorybookConfigEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'storybook_config' })
], StorybookConfigEntity);
exports.StorybookConfigEntity = StorybookConfigEntity;
