# API 客户端配置

## 概述

本文档详细介绍了 DeepCreate 聊天功能的 API 客户端配置，包括请求拦截器、响应拦截器、错误处理、重试机制等。

## 1. 基础配置

### 1.1 Axios 实例配置

```typescript
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useAuthStore } from '@/store/modules/auth';

// API 配置
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:9520',
  timeout: 30000, // 30秒超时
  retryTimes: 3,
  retryDelay: 1000
};

// 创建 Axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求配置接口
interface RequestConfig extends AxiosRequestConfig {
  retry?: boolean;
  retryTimes?: number;
  retryDelay?: number;
  skipAuth?: boolean;
  skipErrorHandler?: boolean;
}
```

### 1.2 请求拦截器

```typescript
// 请求拦截器
apiClient.interceptors.request.use(
  (config: RequestConfig) => {
    // 添加认证头
    if (!config.skipAuth) {
      const authStore = useAuthStore();
      const token = authStore.token;
      
      if (token) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`
        };
      }
    }

    // 添加请求ID用于追踪
    config.headers = {
      ...config.headers,
      'X-Request-ID': generateRequestId()
    };

    // 添加时间戳
    config.metadata = {
      startTime: Date.now()
    };

    // 记录请求日志
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
      params: config.params,
      data: config.data
    });

    return config;
  },
  (error) => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
```

### 1.3 响应拦截器

```typescript
// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 计算请求耗时
    const duration = Date.now() - (response.config.metadata?.startTime || 0);
    
    // 记录响应日志
    console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
      status: response.status,
      duration: `${duration}ms`,
      data: response.data
    });

    // 检查业务状态码
    if (response.data && typeof response.data === 'object') {
      if (response.data.success === false) {
        const error = new Error(response.data.message || '请求失败');
        error.code = response.data.code;
        error.response = response;
        throw error;
      }
    }

    return response;
  },
  async (error) => {
    const config = error.config as RequestConfig;
    
    // 记录错误日志
    console.error('[API Response Error]', {
      url: config?.url,
      method: config?.method,
      status: error.response?.status,
      message: error.message,
      data: error.response?.data
    });

    // 处理特定错误
    await handleResponseError(error);

    // 重试逻辑
    if (shouldRetry(error, config)) {
      return retryRequest(config);
    }

    return Promise.reject(error);
  }
);
```

## 2. 错误处理

### 2.1 响应错误处理

```typescript
async function handleResponseError(error: any) {
  const status = error.response?.status;
  const authStore = useAuthStore();

  switch (status) {
    case 401:
      // Token 过期，清除认证信息
      authStore.logout();
      
      // 跳转到登录页
      if (router.currentRoute.value.path !== '/login') {
        router.push('/login');
      }
      break;

    case 403:
      // 权限不足
      showErrorMessage('权限不足，无法执行此操作');
      break;

    case 429:
      // 请求频率限制
      showErrorMessage('请求过于频繁，请稍后再试');
      break;

    case 500:
    case 502:
    case 503:
    case 504:
      // 服务器错误
      showErrorMessage('服务器暂时不可用，请稍后重试');
      break;

    default:
      // 网络错误
      if (!error.response) {
        if (!navigator.onLine) {
          showErrorMessage('网络连接已断开，请检查网络设置');
        } else {
          showErrorMessage('网络请求失败，请稍后重试');
        }
      }
  }
}
```

### 2.2 重试机制

```typescript
// 判断是否应该重试
function shouldRetry(error: any, config: RequestConfig): boolean {
  // 如果明确禁用重试
  if (config.retry === false) {
    return false;
  }

  // 如果已经重试过
  if (config._retryCount >= (config.retryTimes || API_CONFIG.retryTimes)) {
    return false;
  }

  // 只对特定错误重试
  const retryableErrors = [
    'ECONNABORTED', // 超时
    'ENOTFOUND',    // DNS 解析失败
    'ECONNREFUSED', // 连接被拒绝
    'NETWORK_ERROR' // 网络错误
  ];

  const retryableStatus = [408, 429, 500, 502, 503, 504];

  return (
    retryableErrors.includes(error.code) ||
    retryableStatus.includes(error.response?.status) ||
    !error.response // 网络错误
  );
}

// 重试请求
async function retryRequest(config: RequestConfig): Promise<any> {
  config._retryCount = (config._retryCount || 0) + 1;
  
  const delay = (config.retryDelay || API_CONFIG.retryDelay) * config._retryCount;
  
  console.log(`[API Retry] 第 ${config._retryCount} 次重试，${delay}ms 后执行`);
  
  // 等待指定时间后重试
  await new Promise(resolve => setTimeout(resolve, delay));
  
  return apiClient(config);
}
```

## 3. 请求方法封装

### 3.1 通用请求方法

```typescript
interface RequestOptions<T = any> {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: any;
  params?: any;
  headers?: Record<string, string>;
  timeout?: number;
  retry?: boolean;
  retryTimes?: number;
  skipAuth?: boolean;
  skipErrorHandler?: boolean;
  onUploadProgress?: (progressEvent: any) => void;
  onDownloadProgress?: (progressEvent: any) => void;
  signal?: AbortSignal;
}

// 通用请求方法
async function request<T = any>(options: RequestOptions<T>): Promise<T> {
  const config: RequestConfig = {
    url: options.url,
    method: options.method || 'GET',
    data: options.data,
    params: options.params,
    headers: options.headers,
    timeout: options.timeout,
    retry: options.retry,
    retryTimes: options.retryTimes,
    skipAuth: options.skipAuth,
    skipErrorHandler: options.skipErrorHandler,
    onUploadProgress: options.onUploadProgress,
    onDownloadProgress: options.onDownloadProgress,
    signal: options.signal
  };

  try {
    const response = await apiClient(config);
    return response.data;
  } catch (error) {
    // 如果跳过错误处理，直接抛出
    if (options.skipErrorHandler) {
      throw error;
    }
    
    // 处理错误
    throw await processRequestError(error);
  }
}

// 处理请求错误
async function processRequestError(error: any): Promise<Error> {
  const chatError = ChatErrorFactory.fromHttpError(error);
  
  // 记录到错误监控
  if (chatError.severity === ErrorSeverity.HIGH || chatError.severity === ErrorSeverity.CRITICAL) {
    // 发送到监控服务
    sendErrorToMonitoring(chatError);
  }
  
  return error;
}
```

### 3.2 便捷方法

```typescript
// GET 请求
export function get<T = any>(options: Omit<RequestOptions<T>, 'method'>): Promise<T> {
  return request<T>({ ...options, method: 'GET' });
}

// POST 请求
export function post<T = any>(options: Omit<RequestOptions<T>, 'method'>): Promise<T> {
  return request<T>({ ...options, method: 'POST' });
}

// PUT 请求
export function put<T = any>(options: Omit<RequestOptions<T>, 'method'>): Promise<T> {
  return request<T>({ ...options, method: 'PUT' });
}

// DELETE 请求
export function del<T = any>(options: Omit<RequestOptions<T>, 'method'>): Promise<T> {
  return request<T>({ ...options, method: 'DELETE' });
}

// 文件上传
export function upload<T = any>(
  url: string,
  file: File,
  options: {
    onProgress?: (progress: number) => void;
    data?: Record<string, any>;
  } = {}
): Promise<T> {
  const formData = new FormData();
  formData.append('file', file);
  
  // 添加额外数据
  if (options.data) {
    Object.entries(options.data).forEach(([key, value]) => {
      formData.append(key, value);
    });
  }

  return post<T>({
    url,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (options.onProgress) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        options.onProgress(progress);
      }
    }
  });
}
```

## 4. 流式请求处理

### 4.1 流式请求客户端

```typescript
interface StreamOptions {
  onProgress: (data: any) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  signal?: AbortSignal;
}

class StreamClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    };
  }

  // 设置认证头
  setAuthToken(token: string) {
    this.defaultHeaders.Authorization = `Bearer ${token}`;
  }

  // 流式 POST 请求
  async streamPost(
    url: string,
    data: any,
    options: StreamOptions
  ): Promise<void> {
    const fullUrl = `${this.baseURL}${url}`;
    
    try {
      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: this.defaultHeaders,
        body: JSON.stringify(data),
        signal: options.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      await this.processStream(response, options);
    } catch (error) {
      if (options.onError) {
        options.onError(error as Error);
      } else {
        throw error;
      }
    }
  }

  // 处理流式响应
  private async processStream(
    response: Response,
    options: StreamOptions
  ): Promise<void> {
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    if (!reader) {
      throw new Error('无法获取响应流');
    }

    try {
      while (true) {
        const { value, done } = await reader.read();
        
        if (done) {
          if (options.onComplete) {
            options.onComplete();
          }
          break;
        }

        const text = decoder.decode(value, { stream: true });
        buffer += text;

        // 处理完整的行
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              options.onProgress(data);
            } catch (error) {
              console.warn('解析流式数据失败:', error, line);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }
}

// 创建流式客户端实例
export const streamClient = new StreamClient(API_CONFIG.baseURL);

// 更新认证令牌
const authStore = useAuthStore();
watch(() => authStore.token, (token) => {
  if (token) {
    streamClient.setAuthToken(token);
  }
});
```

### 4.2 流式请求 Hook

```typescript
export function useStreamRequest() {
  const abortController = ref<AbortController | null>(null);
  const isStreaming = ref(false);

  // 发送流式请求
  async function sendStreamRequest(
    url: string,
    data: any,
    onProgress: (data: any) => void,
    onComplete?: () => void,
    onError?: (error: Error) => void
  ) {
    // 取消之前的请求
    if (abortController.value) {
      abortController.value.abort();
    }

    abortController.value = new AbortController();
    isStreaming.value = true;

    try {
      await streamClient.streamPost(url, data, {
        onProgress,
        onComplete: () => {
          isStreaming.value = false;
          if (onComplete) {
            onComplete();
          }
        },
        onError: (error) => {
          isStreaming.value = false;
          if (onError) {
            onError(error);
          }
        },
        signal: abortController.value.signal
      });
    } catch (error) {
      isStreaming.value = false;
      if (onError) {
        onError(error as Error);
      }
    }
  }

  // 取消流式请求
  function cancelStream() {
    if (abortController.value) {
      abortController.value.abort();
      abortController.value = null;
      isStreaming.value = false;
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cancelStream();
  });

  return {
    sendStreamRequest,
    cancelStream,
    isStreaming: readonly(isStreaming)
  };
}
```

## 5. 请求缓存

### 5.1 缓存管理器

```typescript
interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  expiry: number;
}

class RequestCache {
  private cache = new Map<string, CacheItem>();
  private defaultTTL = 5 * 60 * 1000; // 5分钟

  // 生成缓存键
  private generateKey(url: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : '';
    return `${url}:${paramStr}`;
  }

  // 设置缓存
  set<T>(url: string, data: T, params?: any, ttl?: number): void {
    const key = this.generateKey(url, params);
    const expiry = Date.now() + (ttl || this.defaultTTL);
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry
    });
  }

  // 获取缓存
  get<T>(url: string, params?: any): T | null {
    const key = this.generateKey(url, params);
    const item = this.cache.get(key);

    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  // 删除缓存
  delete(url: string, params?: any): void {
    const key = this.generateKey(url, params);
    this.cache.delete(key);
  }

  // 清空缓存
  clear(): void {
    this.cache.clear();
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }
}

// 创建缓存实例
export const requestCache = new RequestCache();

// 定期清理过期缓存
setInterval(() => {
  requestCache.cleanup();
}, 60000); // 每分钟清理一次
```

### 5.2 带缓存的请求方法

```typescript
// 带缓存的 GET 请求
export async function getCached<T = any>(
  options: Omit<RequestOptions<T>, 'method'> & {
    cache?: boolean;
    cacheTTL?: number;
  }
): Promise<T> {
  const { cache = true, cacheTTL, ...requestOptions } = options;

  // 如果启用缓存，先尝试从缓存获取
  if (cache) {
    const cached = requestCache.get<T>(requestOptions.url, requestOptions.params);
    if (cached) {
      console.log(`[Cache Hit] ${requestOptions.url}`);
      return cached;
    }
  }

  // 发送请求
  const data = await get<T>(requestOptions);

  // 缓存结果
  if (cache) {
    requestCache.set(requestOptions.url, data, requestOptions.params, cacheTTL);
  }

  return data;
}
```

## 6. 导出配置

```typescript
// 导出所有 API 相关功能
export {
  apiClient,
  request,
  get,
  post,
  put,
  del,
  upload,
  getCached,
  streamClient,
  requestCache,
  useStreamRequest
};

// 导出配置
export { API_CONFIG };

// 导出类型
export type {
  RequestOptions,
  RequestConfig,
  StreamOptions
};
```
