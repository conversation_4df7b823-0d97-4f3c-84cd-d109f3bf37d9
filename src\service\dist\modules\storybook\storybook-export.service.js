"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var StorybookExportService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookExportService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const puppeteer = require("puppeteer");
const fs = require("fs");
const path = require("path");
const archiver = require("archiver");
const entities_1 = require("./entities");
const config_1 = require("@nestjs/config");
const upload_service_1 = require("../upload/upload.service");
let StorybookExportService = StorybookExportService_1 = class StorybookExportService {
    constructor(storybookRepository, pageRepository, configService, uploadService) {
        this.storybookRepository = storybookRepository;
        this.pageRepository = pageRepository;
        this.configService = configService;
        this.uploadService = uploadService;
        this.logger = new common_1.Logger(StorybookExportService_1.name);
    }
    async exportToPdf(id, userId, options = {}) {
        const { range = 'all', startPage = 1, endPage } = options;
        const storybook = await this.storybookRepository.findOne({
            where: { id, userId },
            relations: ['pages']
        });
        if (!storybook) {
            throw new common_1.NotFoundException('绘本不存在');
        }
        storybook.pages.sort((a, b) => a.pageNumber - b.pageNumber);
        let pagesToExport = storybook.pages;
        if (range === 'custom' && startPage && endPage) {
            pagesToExport = storybook.pages.filter(page => page.pageNumber >= startPage && page.pageNumber <= endPage);
        }
        if (pagesToExport.length === 0) {
            throw new common_1.BadRequestException('没有可导出的页面');
        }
        try {
            const tempDir = path.join(process.cwd(), 'temp', `export_${id}_${Date.now()}`);
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }
            const htmlPath = path.join(tempDir, 'storybook.html');
            const htmlContent = this.generateHtml(storybook, pagesToExport);
            fs.writeFileSync(htmlPath, htmlContent);
            const browser = await puppeteer.launch({
                headless: true,
                args: ['--no-sandbox', '--disable-setuid-sandbox']
            });
            const page = await browser.newPage();
            await page.goto(`file://${htmlPath}`, { waitUntil: 'networkidle0' });
            const pdfPath = path.join(tempDir, 'storybook.pdf');
            await page.pdf({
                path: pdfPath,
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '1cm',
                    right: '1cm',
                    bottom: '1cm',
                    left: '1cm'
                }
            });
            await browser.close();
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const currentDate = `${year}${month}/${day}`;
            const pdfBuffer = fs.readFileSync(pdfPath);
            const filename = `${storybook.title}_${now.getTime()}.pdf`;
            const fileUrl = await this.uploadService.uploadBuffer({
                buffer: pdfBuffer,
                filename,
                dir: `exports/storybooks/${currentDate}`
            });
            fs.unlinkSync(htmlPath);
            fs.unlinkSync(pdfPath);
            fs.rmdirSync(tempDir);
            return {
                url: fileUrl,
                filename
            };
        }
        catch (error) {
            this.logger.error(`导出PDF失败: ${error.message}`, error.stack);
            throw new common_1.BadRequestException('导出PDF失败，请稍后重试');
        }
    }
    async exportToImages(id, userId, options = {}) {
        const { range = 'all', startPage = 1, endPage, quality = 'high' } = options;
        const storybook = await this.storybookRepository.findOne({
            where: { id, userId },
            relations: ['pages']
        });
        if (!storybook) {
            throw new common_1.NotFoundException('绘本不存在');
        }
        storybook.pages.sort((a, b) => a.pageNumber - b.pageNumber);
        let pagesToExport = storybook.pages;
        if (range === 'custom' && startPage && endPage) {
            pagesToExport = storybook.pages.filter(page => page.pageNumber >= startPage && page.pageNumber <= endPage);
        }
        if (pagesToExport.length === 0) {
            throw new common_1.BadRequestException('没有可导出的页面');
        }
        try {
            const tempDir = path.join(process.cwd(), 'temp', `export_${id}_${Date.now()}`);
            const imagesDir = path.join(tempDir, 'images');
            if (!fs.existsSync(imagesDir)) {
                fs.mkdirSync(imagesDir, { recursive: true });
            }
            for (let i = 0; i < pagesToExport.length; i++) {
                const page = pagesToExport[i];
                if (page.imageUrl) {
                    const response = await fetch(page.imageUrl);
                    const buffer = await response.arrayBuffer();
                    const imagePath = path.join(imagesDir, `page_${page.pageNumber}.jpg`);
                    fs.writeFileSync(imagePath, Buffer.from(buffer));
                }
            }
            const zipPath = path.join(tempDir, 'storybook_images.zip');
            const output = fs.createWriteStream(zipPath);
            const archive = archiver('zip', {
                zlib: { level: 9 }
            });
            archive.pipe(output);
            archive.directory(imagesDir, false);
            await archive.finalize();
            await new Promise((resolve) => {
                output.on('close', () => resolve());
            });
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const currentDate = `${year}${month}/${day}`;
            const zipBuffer = fs.readFileSync(zipPath);
            const filename = `${storybook.title}_images_${now.getTime()}.zip`;
            const fileUrl = await this.uploadService.uploadBuffer({
                buffer: zipBuffer,
                filename,
                dir: `exports/storybooks/${currentDate}`
            });
            fs.rmSync(tempDir, { recursive: true, force: true });
            return {
                url: fileUrl,
                filename
            };
        }
        catch (error) {
            this.logger.error(`导出图片集失败: ${error.message}`, error.stack);
            throw new common_1.BadRequestException('导出图片集失败，请稍后重试');
        }
    }
    generateHtml(storybook, pages) {
        let pagesHtml = '';
        for (const page of pages) {
            pagesHtml += `
        <div class="page">
          <div class="page-number">${page.pageNumber}</div>
          ${page.imageUrl ? `<div class="page-image"><img src="${page.imageUrl}" alt="Page ${page.pageNumber}"></div>` : ''}
          <div class="page-text">${page.text || ''}</div>
        </div>
      `;
        }
        return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${storybook.title}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
          }
          .cover {
            text-align: center;
            padding: 40px;
            page-break-after: always;
          }
          .cover h1 {
            font-size: 24px;
            margin-bottom: 20px;
          }
          .cover-image {
            max-width: 80%;
            max-height: 400px;
            margin-bottom: 20px;
          }
          .page {
            page-break-after: always;
            padding: 20px;
            position: relative;
          }
          .page-number {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 12px;
            color: #999;
          }
          .page-image {
            text-align: center;
            margin-bottom: 20px;
          }
          .page-image img {
            max-width: 100%;
            max-height: 400px;
          }
          .page-text {
            font-size: 14px;
            line-height: 1.5;
          }
        </style>
      </head>
      <body>
        <div class="cover">
          <h1>${storybook.title}</h1>
          ${storybook.coverImg ? `<img class="cover-image" src="${storybook.coverImg}" alt="Cover">` : ''}
          <p>${storybook.description || ''}</p>
        </div>
        ${pagesHtml}
      </body>
      </html>
    `;
    }
};
StorybookExportService = StorybookExportService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.StorybookEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.StorybookPageEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        config_1.ConfigService,
        upload_service_1.UploadService])
], StorybookExportService);
exports.StorybookExportService = StorybookExportService;
