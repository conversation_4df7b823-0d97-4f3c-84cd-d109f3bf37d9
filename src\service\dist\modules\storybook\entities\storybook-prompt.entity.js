"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookPromptEntity = void 0;
const baseEntity_1 = require("../../../common/entity/baseEntity");
const typeorm_1 = require("typeorm");
let StorybookPromptEntity = class StorybookPromptEntity extends baseEntity_1.BaseEntity {
};
__decorate([
    (0, typeorm_1.Column)({ comment: '提示词标题' }),
    __metadata("design:type", String)
], StorybookPromptEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '提示词内容', type: 'text' }),
    __metadata("design:type", String)
], StorybookPromptEntity.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '提示词类型', nullable: true }),
    __metadata("design:type", String)
], StorybookPromptEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '状态(0:禁用,1:启用)', default: 1 }),
    __metadata("design:type", Number)
], StorybookPromptEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '排序', default: 100 }),
    __metadata("design:type", Number)
], StorybookPromptEntity.prototype, "order", void 0);
StorybookPromptEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'storybook_prompt' })
], StorybookPromptEntity);
exports.StorybookPromptEntity = StorybookPromptEntity;
