"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRechargeDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class UserRechargeDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1, description: '用户id', required: true }),
    (0, class_validator_1.IsDefined)({ message: '用户id是必传参数' }),
    __metadata("design:type", Number)
], UserRechargeDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 100, description: '用户对话模型3次数', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UserRechargeDto.prototype, "model3Count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 5, description: '用户对话模型4次数', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UserRechargeDto.prototype, "model4Count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0, description: '用户MJ额度', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UserRechargeDto.prototype, "drawMjCount", void 0);
exports.UserRechargeDto = UserRechargeDto;
