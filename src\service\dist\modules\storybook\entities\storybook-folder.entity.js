"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookFolderEntity = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../../common/entities/base.entity");
const storybook_entity_1 = require("./storybook.entity");
let StorybookFolderEntity = class StorybookFolderEntity extends base_entity_1.BaseEntity {
};
__decorate([
    (0, typeorm_1.Column)({ comment: '文件夹名称' }),
    __metadata("design:type", String)
], StorybookFolderEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '文件夹描述', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookFolderEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '封面图片URL', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookFolderEntity.prototype, "coverImg", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '创建用户ID' }),
    __metadata("design:type", Number)
], StorybookFolderEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '排序', default: 0 }),
    __metadata("design:type", Number)
], StorybookFolderEntity.prototype, "order", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '文件夹颜色', nullable: true }),
    __metadata("design:type", String)
], StorybookFolderEntity.prototype, "color", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '文件夹图标', nullable: true }),
    __metadata("design:type", String)
], StorybookFolderEntity.prototype, "icon", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否为系统文件夹', default: 0 }),
    __metadata("design:type", Number)
], StorybookFolderEntity.prototype, "isSystem", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => storybook_entity_1.StorybookEntity, storybook => storybook.folder),
    __metadata("design:type", Array)
], StorybookFolderEntity.prototype, "storybooks", void 0);
StorybookFolderEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'storybook_folder' })
], StorybookFolderEntity);
exports.StorybookFolderEntity = StorybookFolderEntity;
