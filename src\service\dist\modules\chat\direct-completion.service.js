"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DirectCompletionService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("axios");
const utils_1 = require("../../common/utils");
const models_service_1 = require("../models/models.service");
const user_service_1 = require("../user/user.service");
const userBalance_service_1 = require("../userBalance/userBalance.service");
const globalConfig_service_1 = require("../globalConfig/globalConfig.service");
let DirectCompletionService = class DirectCompletionService {
    constructor(modelsService, userService, userBalanceService, globalConfigService) {
        this.modelsService = modelsService;
        this.userService = userService;
        this.userBalanceService = userBalanceService;
        this.globalConfigService = globalConfigService;
    }
    async directCompletion(body, req) {
        try {
            await this.userService.checkUserStatus(req.user);
            await this.userBalanceService.checkUserCertification(req.user.id);
            const { messages, model, temperature = 0.3, stream = false } = body;
            const currentRequestModelKey = await this.modelsService.getCurrentModelKeyInfo(model || 'gpt-3.5-turbo');
            if (!currentRequestModelKey) {
                throw new common_1.HttpException('未找到指定的模型', common_1.HttpStatus.BAD_REQUEST);
            }
            const { key, proxyUrl, deduct, deductType, timeout, model: useModel, } = currentRequestModelKey;
            const { openaiBaseUrl, openaiBaseKey, openaiTimeout } = await this.globalConfigService.getConfigs([
                'openaiBaseUrl',
                'openaiBaseKey',
                'openaiTimeout',
            ]);
            await this.userBalanceService.validateBalance(req, deductType, deduct);
            const modelKey = key || openaiBaseKey;
            const proxyResUrl = (0, utils_1.formatUrl)(proxyUrl || openaiBaseUrl || 'https://api.openai.com');
            const modelTimeout = (timeout || openaiTimeout || 300) * 1000;
            common_1.Logger.log(`直接调用模型API，模型: ${useModel}, 温度: ${temperature}`, 'DirectCompletionService');
            const options = {
                method: 'POST',
                url: `${proxyResUrl}/v1/chat/completions`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${modelKey}`,
                },
                timeout: modelTimeout,
                data: {
                    model: useModel,
                    messages,
                    temperature,
                    stream,
                },
            };
            const response = await (0, axios_1.default)(options);
            await this.userBalanceService.deductFromBalance(req.user.id, deductType, deduct);
            if (stream) {
                return response.data;
            }
            else {
                return response.data.choices[0].message.content;
            }
        }
        catch (error) {
            common_1.Logger.error(`直接调用模型API失败: ${error.message}`, error.stack, 'DirectCompletionService');
            throw new common_1.HttpException(`调用模型API失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
DirectCompletionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [models_service_1.ModelsService,
        user_service_1.UserService,
        userBalance_service_1.UserBalanceService,
        globalConfig_service_1.GlobalConfigService])
], DirectCompletionService);
exports.DirectCompletionService = DirectCompletionService;
