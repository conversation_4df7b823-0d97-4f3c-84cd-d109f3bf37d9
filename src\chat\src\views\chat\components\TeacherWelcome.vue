<template>
  <div class="teacher-welcome h-full flex flex-col relative overflow-hidden">
    <!-- 美化的背景装饰 -->
    <div class="absolute inset-0 pointer-events-none">
      <!-- 主背景渐变 -->
      <div class="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-indigo-50/30 to-purple-50/20 dark:from-blue-900/30 dark:via-indigo-900/20 dark:to-purple-900/15"></div>

      <!-- 浮动装饰元素 -->
      <div class="absolute top-1/4 left-1/4 w-24 h-24 bg-gradient-to-br from-blue-400/8 to-indigo-500/4 rounded-full blur-xl animate-pulse"></div>
      <div class="absolute bottom-1/3 right-1/4 w-20 h-20 bg-gradient-to-br from-purple-400/8 to-pink-500/4 rounded-full blur-lg"></div>
      <div class="absolute top-1/2 right-1/3 w-16 h-16 bg-gradient-to-br from-green-400/8 to-teal-500/4 rounded-full blur-md"></div>
    </div>

    <!-- 增强的欢迎头部 -->
    <div class="welcome-header text-center py-12 px-6 relative z-10">
      <div class="avatar-section mb-8">
        <div class="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center text-5xl text-white shadow-2xl shadow-blue-500/25 dark:shadow-blue-600/30 transform transition-all duration-500 hover:scale-110 hover:rotate-3 ai-float">
          🤖
        </div>
      </div>

      <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-800 via-blue-600 to-indigo-700 dark:from-gray-100 dark:via-blue-400 dark:to-indigo-300 bg-clip-text text-transparent mb-4 leading-tight">
        AI教学助手
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto">
        您的智能教学伙伴，随时为您提供
        <span class="font-semibold text-blue-600 dark:text-blue-400">专业的教学支持</span>
        和创新的教学方案
      </p>

      <!-- 特色功能标签 -->
      <div class="flex flex-wrap justify-center gap-3 mb-8">
        <span class="px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium border border-blue-200 dark:border-blue-700/50 hover:bg-blue-200 dark:hover:bg-blue-800/40 transition-colors cursor-default">
          💡 智能问答
        </span>
        <span class="px-4 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-sm font-medium border border-green-200 dark:border-green-700/50 hover:bg-green-200 dark:hover:bg-green-800/40 transition-colors cursor-default">
          📚 教案生成
        </span>
        <span class="px-4 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-sm font-medium border border-purple-200 dark:border-purple-700/50 hover:bg-purple-200 dark:hover:bg-purple-800/40 transition-colors cursor-default">
          🎯 个性化指导
        </span>
      </div>
    </div>

    <!-- 增强的功能介绍 -->
    <div class="features-section flex-1 px-6 relative z-10">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <!-- 教学咨询 -->
        <div class="feature-card bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-white/50 dark:border-gray-700/50">
          <div class="text-center">
            <div class="w-12 h-12 mx-auto bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-2xl text-white mb-4 shadow-lg">
              💬
            </div>
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">教学咨询</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">获取专业的教学方法和策略建议</p>
          </div>
        </div>

        <!-- 课程设计 -->
        <div class="feature-card bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-white/50 dark:border-gray-700/50">
          <div class="text-center">
            <div class="w-12 h-12 mx-auto bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-2xl text-white mb-4 shadow-lg">
              📚
            </div>
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">课程设计</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">创建详细的教学计划和教案</p>
          </div>
        </div>

        <!-- 教学方法 -->
        <div class="feature-card bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-white/50 dark:border-gray-700/50">
          <div class="text-center">
            <div class="w-12 h-12 mx-auto bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-2xl text-white mb-4 shadow-lg">
              🎯
            </div>
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">教学方法</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">探索创新的教学方法和模式</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 美化的底部提示 -->
    <div class="welcome-footer text-center py-6 px-6 border-t border-white/40 dark:border-gray-600/40 backdrop-blur-md relative z-10">
      <div class="bg-gradient-to-r from-blue-50/80 to-indigo-50/60 dark:from-gray-800/80 dark:to-gray-700/60 rounded-xl p-4 backdrop-blur-sm border border-blue-200/30 dark:border-gray-600/30">
        <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
          💡 <span class="font-medium">开始对话：</span>在下方输入框中描述您的教学需求，我将为您提供专业的指导和建议
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件现在主要用于显示欢迎信息和功能介绍
// 快速开始功能已移动到右侧边栏
</script>

<style scoped>
.teacher-welcome {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.dark .teacher-welcome {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.feature-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .feature-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* AI浮动动画 */
@keyframes ai-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
}

.ai-float {
  animation: ai-float 3s ease-in-out infinite;
}

/* 功能卡片进入动画 */
.feature-card {
  animation: slideInUp 0.6s ease-out forwards;
}

.feature-card:nth-child(1) {
  animation-delay: 0.1s;
}

.feature-card:nth-child(2) {
  animation-delay: 0.2s;
}

.feature-card:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标签悬浮效果 */
span[class*="bg-"] {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

span[class*="bg-"]:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .feature-card {
    margin-bottom: 1rem;
  }

  .welcome-header {
    padding: 2rem 1rem;
  }

  .features-section {
    padding: 1rem;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0,-8px,0);
  }
  70% {
    transform: translate3d(0,-4px,0);
  }
  90% {
    transform: translate3d(0,-2px,0);
  }
}

.animate-bounce {
  animation: bounce 2s infinite;
}
</style>
