"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookTemplateEntity = void 0;
const baseEntity_1 = require("../../../common/entity/baseEntity");
const typeorm_1 = require("typeorm");
let StorybookTemplateEntity = class StorybookTemplateEntity extends baseEntity_1.BaseEntity {
};
__decorate([
    (0, typeorm_1.Column)({ comment: '模板标题' }),
    __metadata("design:type", String)
], StorybookTemplateEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '模板描述', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookTemplateEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '封面图片URL', type: 'text', nullable: true }),
    __metadata("design:type", String)
], StorybookTemplateEntity.prototype, "coverImg", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '分类', nullable: true }),
    __metadata("design:type", String)
], StorybookTemplateEntity.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '模板内容', type: 'json' }),
    __metadata("design:type", Object)
], StorybookTemplateEntity.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '状态(0:禁用,1:启用)', default: 1 }),
    __metadata("design:type", Number)
], StorybookTemplateEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '排序', default: 100 }),
    __metadata("design:type", Number)
], StorybookTemplateEntity.prototype, "order", void 0);
StorybookTemplateEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'storybook_template' })
], StorybookTemplateEntity);
exports.StorybookTemplateEntity = StorybookTemplateEntity;
