"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorybookModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const storybook_service_1 = require("./storybook.service");
const storybook_works_service_1 = require("./storybook-works.service");
const storybook_export_service_1 = require("./storybook-export.service");
const storybook_controller_1 = require("./storybook.controller");
const storybook_admin_controller_1 = require("./storybook-admin.controller");
const storybook_works_controller_1 = require("./storybook-works.controller");
const badWords_module_1 = require("../badWords/badWords.module");
const upload_module_1 = require("../upload/upload.module");
const entities_1 = require("./entities");
let StorybookModule = class StorybookModule {
};
StorybookModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.StorybookEntity,
                entities_1.StorybookPageEntity,
                entities_1.StorybookCharacterEntity,
                entities_1.StorybookTemplateEntity,
                entities_1.StorybookStatisticEntity,
                entities_1.StorybookPromptEntity,
                entities_1.StorybookConfigEntity,
                entities_1.StorybookImageEntity,
                entities_1.StorybookFolderEntity
            ]),
            config_1.ConfigModule,
            badWords_module_1.BadWordsModule,
            upload_module_1.UploadModule,
        ],
        controllers: [storybook_controller_1.StorybookController, storybook_admin_controller_1.StorybookAdminController, storybook_works_controller_1.StorybookWorksController],
        providers: [storybook_service_1.StorybookService, storybook_works_service_1.StorybookWorksService, storybook_export_service_1.StorybookExportService],
        exports: [storybook_service_1.StorybookService, storybook_works_service_1.StorybookWorksService, storybook_export_service_1.StorybookExportService],
    })
], StorybookModule);
exports.StorybookModule = StorybookModule;
