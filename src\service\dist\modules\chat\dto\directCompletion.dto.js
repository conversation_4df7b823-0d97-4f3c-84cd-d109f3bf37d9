"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DirectCompletionDto = exports.MessageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class MessageDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'system', description: '消息角色' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MessageDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '你是一个专业的SQL生成器', description: '消息内容' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MessageDto.prototype, "content", void 0);
exports.MessageDto = MessageDto;
class DirectCompletionDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        example: [
            { role: 'system', content: '你是一个专业的SQL生成器' },
            { role: 'user', content: '请生成一个创建用户表的SQL' }
        ],
        description: '消息数组'
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNotEmpty)({ message: '消息数组不能为空' }),
    __metadata("design:type", Array)
], DirectCompletionDto.prototype, "messages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'gpt-3.5-turbo', description: '使用的模型', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DirectCompletionDto.prototype, "model", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0.3, description: '温度值', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DirectCompletionDto.prototype, "temperature", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: false, description: '是否使用流式响应', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DirectCompletionDto.prototype, "stream", void 0);
exports.DirectCompletionDto = DirectCompletionDto;
