"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCharacterDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateCharacterDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所属绘本ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateCharacterDto.prototype, "storybookId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '角色名称' }),
    (0, class_validator_1.IsNotEmpty)({ message: '角色名称不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCharacterDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '角色类型', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCharacterDto.prototype, "characterType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '外观描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCharacterDto.prototype, "appearance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '性格特点', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateCharacterDto.prototype, "personalityTraits", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '外观特点', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateCharacterDto.prototype, "appearanceTraits", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '角色图片URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCharacterDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为模板(0:否,1:是)', required: false, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateCharacterDto.prototype, "isTemplate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '角色标签', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateCharacterDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否收藏(0:否,1:是)', required: false, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateCharacterDto.prototype, "isFavorite", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '角色在故事中的角色', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCharacterDto.prototype, "role", void 0);
exports.CreateCharacterDto = CreateCharacterDto;
