# 流式响应解析工具

## 概述

本文档详细介绍了 DeepCreate 项目中流式响应数据的解析工具，包括数据解析、缓冲处理、错误恢复等功能。

## 1. 基础解析器

### 1.1 流式数据解析器

```typescript
interface StreamData {
  text?: string;
  userBalance?: number;
  chatId?: number;
  fileInfo?: string;
  error?: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  [key: string]: any;
}

interface ParseOptions {
  encoding?: string;
  bufferSize?: number;
  maxLineLength?: number;
  skipEmptyLines?: boolean;
  validateJson?: boolean;
}

class StreamParser {
  private buffer = '';
  private decoder: TextDecoder;
  private options: Required<ParseOptions>;

  constructor(options: ParseOptions = {}) {
    this.options = {
      encoding: 'utf-8',
      bufferSize: 8192,
      maxLineLength: 10000,
      skipEmptyLines: true,
      validateJson: true,
      ...options
    };
    
    this.decoder = new TextDecoder(this.options.encoding);
  }

  // 解析流式数据块
  parseChunk(chunk: Uint8Array): StreamData[] {
    const text = this.decoder.decode(chunk, { stream: true });
    this.buffer += text;

    return this.processBuffer();
  }

  // 处理缓冲区数据
  private processBuffer(): StreamData[] {
    const results: StreamData[] = [];
    const lines = this.buffer.split('\n');
    
    // 保留最后一个可能不完整的行
    this.buffer = lines.pop() || '';

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 跳过空行
      if (this.options.skipEmptyLines && !trimmedLine) {
        continue;
      }

      // 检查行长度
      if (trimmedLine.length > this.options.maxLineLength) {
        console.warn('行长度超过限制，跳过:', trimmedLine.substring(0, 100) + '...');
        continue;
      }

      // 解析 JSON
      const parsed = this.parseJsonLine(trimmedLine);
      if (parsed) {
        results.push(parsed);
      }
    }

    return results;
  }

  // 解析 JSON 行
  private parseJsonLine(line: string): StreamData | null {
    if (!line) return null;

    try {
      const data = JSON.parse(line);
      
      // 验证数据格式
      if (this.options.validateJson && !this.validateStreamData(data)) {
        console.warn('无效的流式数据格式:', data);
        return null;
      }

      return data as StreamData;
    } catch (error) {
      console.warn('JSON 解析失败:', error, line);
      return null;
    }
  }

  // 验证流式数据格式
  private validateStreamData(data: any): boolean {
    if (typeof data !== 'object' || data === null) {
      return false;
    }

    // 检查是否包含有效字段
    const validFields = ['text', 'userBalance', 'chatId', 'fileInfo', 'error', 'usage'];
    return validFields.some(field => field in data);
  }

  // 完成解析（处理剩余缓冲区）
  finish(): StreamData[] {
    if (!this.buffer.trim()) {
      return [];
    }

    const results = this.processBuffer();
    
    // 处理最后的不完整数据
    if (this.buffer.trim()) {
      const parsed = this.parseJsonLine(this.buffer.trim());
      if (parsed) {
        results.push(parsed);
      }
    }

    this.reset();
    return results;
  }

  // 重置解析器
  reset(): void {
    this.buffer = '';
  }

  // 获取缓冲区状态
  getBufferInfo(): { size: number; content: string } {
    return {
      size: this.buffer.length,
      content: this.buffer
    };
  }
}
```

### 1.2 增强型流式解析器

```typescript
interface EnhancedParseOptions extends ParseOptions {
  onProgress?: (data: StreamData) => void;
  onError?: (error: Error, line: string) => void;
  onComplete?: () => void;
  enableRecovery?: boolean;
  recoveryAttempts?: number;
}

class EnhancedStreamParser extends StreamParser {
  private options: Required<EnhancedParseOptions>;
  private totalProcessed = 0;
  private errorCount = 0;
  private recoveryAttempts = 0;

  constructor(options: EnhancedParseOptions = {}) {
    const enhancedOptions = {
      enableRecovery: true,
      recoveryAttempts: 3,
      ...options
    };
    
    super(enhancedOptions);
    this.options = enhancedOptions as Required<EnhancedParseOptions>;
  }

  // 解析流式数据块（增强版）
  parseChunk(chunk: Uint8Array): StreamData[] {
    try {
      const results = super.parseChunk(chunk);
      
      // 处理每个解析结果
      for (const data of results) {
        this.totalProcessed++;
        
        if (this.options.onProgress) {
          this.options.onProgress(data);
        }
      }

      return results;
    } catch (error) {
      this.handleParseError(error as Error);
      return [];
    }
  }

  // 处理解析错误
  private handleParseError(error: Error): void {
    this.errorCount++;
    
    if (this.options.onError) {
      this.options.onError(error, this.getBufferInfo().content);
    }

    // 尝试恢复
    if (this.options.enableRecovery && this.recoveryAttempts < this.options.recoveryAttempts) {
      this.attemptRecovery();
    }
  }

  // 尝试恢复解析
  private attemptRecovery(): void {
    this.recoveryAttempts++;
    
    console.log(`尝试恢复解析，第 ${this.recoveryAttempts} 次`);
    
    // 清理缓冲区中的无效数据
    const buffer = this.getBufferInfo().content;
    const lines = buffer.split('\n');
    
    // 找到第一个有效的 JSON 行
    for (let i = 0; i < lines.length; i++) {
      try {
        JSON.parse(lines[i].trim());
        // 找到有效行，保留从这里开始的数据
        this.buffer = lines.slice(i).join('\n');
        console.log('恢复成功，保留有效数据');
        return;
      } catch {
        // 继续查找
      }
    }
    
    // 如果没有找到有效数据，清空缓冲区
    this.reset();
    console.log('恢复失败，清空缓冲区');
  }

  // 完成解析（增强版）
  finish(): StreamData[] {
    const results = super.finish();
    
    if (this.options.onComplete) {
      this.options.onComplete();
    }

    return results;
  }

  // 获取解析统计信息
  getStats(): {
    totalProcessed: number;
    errorCount: number;
    recoveryAttempts: number;
    successRate: number;
  } {
    return {
      totalProcessed: this.totalProcessed,
      errorCount: this.errorCount,
      recoveryAttempts: this.recoveryAttempts,
      successRate: this.totalProcessed > 0 ? 
        (this.totalProcessed - this.errorCount) / this.totalProcessed : 0
    };
  }
}
```

## 2. 专用解析器

### 2.1 聊天流式解析器

```typescript
interface ChatStreamData extends StreamData {
  messageType?: 'text' | 'image' | 'audio' | 'file';
  isComplete?: boolean;
  metadata?: Record<string, any>;
}

class ChatStreamParser extends EnhancedStreamParser {
  private currentMessage = '';
  private messageMetadata: Record<string, any> = {};

  // 解析聊天流式数据
  parseChatChunk(chunk: Uint8Array): {
    textDelta: string;
    fullText: string;
    metadata: Record<string, any>;
    isComplete: boolean;
  } {
    const results = this.parseChunk(chunk);
    let textDelta = '';
    let isComplete = false;

    for (const data of results) {
      if (data.text) {
        textDelta += data.text;
        this.currentMessage += data.text;
      }

      // 更新元数据
      if (data.userBalance !== undefined) {
        this.messageMetadata.userBalance = data.userBalance;
      }
      
      if (data.chatId) {
        this.messageMetadata.chatId = data.chatId;
      }
      
      if (data.fileInfo) {
        this.messageMetadata.fileInfo = data.fileInfo;
      }
      
      if (data.usage) {
        this.messageMetadata.usage = data.usage;
        isComplete = true; // usage 通常表示消息完成
      }

      if (data.error) {
        this.messageMetadata.error = data.error;
        isComplete = true;
      }
    }

    return {
      textDelta,
      fullText: this.currentMessage,
      metadata: { ...this.messageMetadata },
      isComplete
    };
  }

  // 重置消息状态
  resetMessage(): void {
    this.currentMessage = '';
    this.messageMetadata = {};
    this.reset();
  }

  // 获取当前消息状态
  getCurrentMessage(): {
    text: string;
    metadata: Record<string, any>;
  } {
    return {
      text: this.currentMessage,
      metadata: { ...this.messageMetadata }
    };
  }
}
```

### 2.2 多模态流式解析器

```typescript
interface MultiModalStreamData extends StreamData {
  type: 'text' | 'image' | 'audio' | 'video' | 'file';
  content: any;
  progress?: number;
  status?: 'processing' | 'completed' | 'failed';
}

class MultiModalStreamParser extends EnhancedStreamParser {
  private contentBuffers = new Map<string, any>();

  // 解析多模态数据
  parseMultiModalChunk(chunk: Uint8Array): MultiModalStreamData[] {
    const results = this.parseChunk(chunk);
    const multiModalResults: MultiModalStreamData[] = [];

    for (const data of results) {
      const multiModalData = this.processMultiModalData(data);
      if (multiModalData) {
        multiModalResults.push(multiModalData);
      }
    }

    return multiModalResults;
  }

  // 处理多模态数据
  private processMultiModalData(data: StreamData): MultiModalStreamData | null {
    // 文本数据
    if (data.text) {
      return {
        type: 'text',
        content: data.text,
        ...data
      };
    }

    // 图像数据
    if (data.fileInfo) {
      try {
        const fileInfo = JSON.parse(data.fileInfo);
        if (fileInfo.type?.startsWith('image/')) {
          return {
            type: 'image',
            content: fileInfo,
            status: 'completed',
            ...data
          };
        }
      } catch (error) {
        console.warn('解析文件信息失败:', error);
      }
    }

    // 音频数据
    if (data.audioUrl || data.ttsUrl) {
      return {
        type: 'audio',
        content: {
          url: data.audioUrl || data.ttsUrl,
          duration: data.duration
        },
        status: 'completed',
        ...data
      };
    }

    // 视频数据
    if (data.videoUrl) {
      return {
        type: 'video',
        content: {
          url: data.videoUrl,
          duration: data.duration
        },
        status: 'completed',
        ...data
      };
    }

    return null;
  }
}
```

## 3. 解析器工厂

### 3.1 解析器工厂类

```typescript
enum ParserType {
  BASIC = 'basic',
  ENHANCED = 'enhanced',
  CHAT = 'chat',
  MULTIMODAL = 'multimodal'
}

class StreamParserFactory {
  // 创建解析器
  static create(
    type: ParserType,
    options: EnhancedParseOptions = {}
  ): StreamParser {
    switch (type) {
      case ParserType.BASIC:
        return new StreamParser(options);
      
      case ParserType.ENHANCED:
        return new EnhancedStreamParser(options);
      
      case ParserType.CHAT:
        return new ChatStreamParser(options);
      
      case ParserType.MULTIMODAL:
        return new MultiModalStreamParser(options);
      
      default:
        throw new Error(`不支持的解析器类型: ${type}`);
    }
  }

  // 创建聊天解析器（便捷方法）
  static createChatParser(options: {
    onTextUpdate?: (text: string, delta: string) => void;
    onMetadataUpdate?: (metadata: Record<string, any>) => void;
    onComplete?: () => void;
    onError?: (error: Error) => void;
  } = {}): ChatStreamParser {
    return new ChatStreamParser({
      onProgress: (data) => {
        // 处理文本更新
        if (data.text && options.onTextUpdate) {
          const parser = this as any;
          options.onTextUpdate(parser.currentMessage, data.text);
        }
        
        // 处理元数据更新
        if (options.onMetadataUpdate) {
          const metadata: Record<string, any> = {};
          if (data.userBalance !== undefined) metadata.userBalance = data.userBalance;
          if (data.chatId) metadata.chatId = data.chatId;
          if (data.fileInfo) metadata.fileInfo = data.fileInfo;
          if (data.usage) metadata.usage = data.usage;
          
          if (Object.keys(metadata).length > 0) {
            options.onMetadataUpdate(metadata);
          }
        }
      },
      onComplete: options.onComplete,
      onError: options.onError
    });
  }
}
```

## 4. 使用示例

### 4.1 基础使用

```typescript
// 创建解析器
const parser = StreamParserFactory.create(ParserType.CHAT, {
  onProgress: (data) => {
    console.log('解析到数据:', data);
  },
  onError: (error, line) => {
    console.error('解析错误:', error, line);
  },
  onComplete: () => {
    console.log('解析完成');
  }
});

// 处理流式数据
async function processStream(response: Response) {
  const reader = response.body?.getReader();
  
  if (!reader) {
    throw new Error('无法获取响应流');
  }

  try {
    while (true) {
      const { value, done } = await reader.read();
      
      if (done) {
        parser.finish();
        break;
      }

      parser.parseChunk(value);
    }
  } finally {
    reader.releaseLock();
  }
}
```

### 4.2 Vue 组合式函数

```typescript
export function useStreamParser(type: ParserType = ParserType.CHAT) {
  const parser = ref<StreamParser | null>(null);
  const isProcessing = ref(false);
  const stats = ref({
    totalProcessed: 0,
    errorCount: 0,
    successRate: 0
  });

  // 初始化解析器
  function initParser(options: EnhancedParseOptions = {}) {
    parser.value = StreamParserFactory.create(type, {
      ...options,
      onProgress: (data) => {
        // 更新统计信息
        if (parser.value instanceof EnhancedStreamParser) {
          stats.value = parser.value.getStats();
        }
        
        // 调用用户回调
        if (options.onProgress) {
          options.onProgress(data);
        }
      },
      onError: (error, line) => {
        console.error('流式解析错误:', error);
        if (options.onError) {
          options.onError(error, line);
        }
      },
      onComplete: () => {
        isProcessing.value = false;
        if (options.onComplete) {
          options.onComplete();
        }
      }
    });
  }

  // 处理数据块
  function processChunk(chunk: Uint8Array) {
    if (!parser.value) {
      throw new Error('解析器未初始化');
    }

    isProcessing.value = true;
    return parser.value.parseChunk(chunk);
  }

  // 完成处理
  function finish() {
    if (parser.value) {
      const results = parser.value.finish();
      isProcessing.value = false;
      return results;
    }
    return [];
  }

  // 重置解析器
  function reset() {
    if (parser.value) {
      parser.value.reset();
    }
    isProcessing.value = false;
    stats.value = {
      totalProcessed: 0,
      errorCount: 0,
      successRate: 0
    };
  }

  return {
    initParser,
    processChunk,
    finish,
    reset,
    isProcessing: readonly(isProcessing),
    stats: readonly(stats)
  };
}
```

## 5. 导出

```typescript
export {
  StreamParser,
  EnhancedStreamParser,
  ChatStreamParser,
  MultiModalStreamParser,
  StreamParserFactory,
  ParserType,
  useStreamParser
};

export type {
  StreamData,
  ParseOptions,
  EnhancedParseOptions,
  ChatStreamData,
  MultiModalStreamData
};
```
