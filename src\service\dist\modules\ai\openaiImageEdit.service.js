"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIImageEditService = void 0;
const common_1 = require("@nestjs/common");
const globalConfig_service_1 = require("../../modules/globalConfig/globalConfig.service");
const chatLog_service_1 = require("../chatLog/chatLog.service");
const upload_service_1 = require("../upload/upload.service");
const openaiChat_service_1 = require("./openaiChat.service");
const axios_1 = require("axios");
const FormData = require("form-data");
const fs = require("fs");
const path = require("path");
const os = require("os");
const crypto = require("crypto");
let OpenAIImageEditService = class OpenAIImageEditService {
    constructor(globalConfigService, chatLogService, uploadService, openAIChatService) {
        this.globalConfigService = globalConfigService;
        this.chatLogService = chatLogService;
        this.uploadService = uploadService;
        this.openAIChatService = openAIChatService;
    }
    async editImage(inputs) {
        var _a, _b, _c, _d;
        common_1.Logger.log(`开始提交 OpenAI 图像编辑任务，模型: ${inputs.model}`, 'OpenAIImageEditService');
        const { apiKey, model, proxyUrl, prompt, extraParam, timeout, onSuccess, onFailure, groupId, assistantLogId, modelName, } = inputs;
        const configs = await this.globalConfigService.getConfigs([
            'isConvertToBase64',
        ]);
        const isConvertToBase64 = (configs === null || configs === void 0 ? void 0 : configs.isConvertToBase64) || '0';
        const size = (extraParam === null || extraParam === void 0 ? void 0 : extraParam.size) || '1024x1024';
        const quality = (extraParam === null || extraParam === void 0 ? void 0 : extraParam.quality) || 'standard';
        const responseFormat = isConvertToBase64 === '1' ? 'b64_json' : 'url';
        const imageUrl = extraParam === null || extraParam === void 0 ? void 0 : extraParam.image;
        const maskBase64 = extraParam === null || extraParam === void 0 ? void 0 : extraParam.mask;
        let result = {
            answer: '',
            fileInfo: '',
            status: 2,
            drawId: '',
            customId: '',
            progress: '0%'
        };
        if (assistantLogId) {
            await this.chatLogService.updateChatLog(assistantLogId, {
                answer: '图像编辑中...',
                progress: '10%',
                status: 2,
            });
        }
        try {
            if (!imageUrl) {
                throw new Error('缺少图像参数');
            }
            if (!maskBase64) {
                throw new Error('缺少蒙版参数');
            }
            const tempDir = path.join(os.tmpdir(), 'openai-image-edit');
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }
            common_1.Logger.log('处理原始图像', 'OpenAIImageEditService');
            let imageBuffer;
            let imageFormat = 'png';
            let imagePath;
            let maskPath;
            try {
                if (imageUrl.startsWith('data:')) {
                    const matches = imageUrl.match(/^data:image\/([a-zA-Z0-9]+);base64,(.*)$/);
                    if (!matches || matches.length !== 3) {
                        throw new Error('无效的Base64图像格式');
                    }
                    imageFormat = matches[1];
                    const base64Data = matches[2];
                    imageBuffer = Buffer.from(base64Data, 'base64');
                    const sizeInMB = imageBuffer.length / (1024 * 1024);
                    if (sizeInMB > 25) {
                        throw new Error(`图像大小超过25MB限制 (${sizeInMB.toFixed(2)}MB)`);
                    }
                }
                else {
                    common_1.Logger.log(`从URL下载图像: ${imageUrl.substring(0, 50)}...`, 'OpenAIImageEditService');
                    const imageResponse = await axios_1.default.get(imageUrl, {
                        responseType: 'arraybuffer',
                        timeout: 30000,
                        maxContentLength: 25 * 1024 * 1024
                    });
                    imageBuffer = Buffer.from(imageResponse.data);
                    const sizeInMB = imageBuffer.length / (1024 * 1024);
                    if (sizeInMB > 25) {
                        throw new Error(`图像大小超过25MB限制 (${sizeInMB.toFixed(2)}MB)`);
                    }
                }
                const imageFileName = `image-${crypto.randomBytes(8).toString('hex')}.${imageFormat}`;
                imagePath = path.join(tempDir, imageFileName);
                fs.writeFileSync(imagePath, imageBuffer);
                common_1.Logger.log(`图像已保存到临时文件: ${imagePath}`, 'OpenAIImageEditService');
            }
            catch (error) {
                common_1.Logger.error(`处理原始图像失败: ${error.message}`, error, 'OpenAIImageEditService');
                throw new Error(`处理原始图像失败: ${error.message}`);
            }
            common_1.Logger.log('处理蒙版图像', 'OpenAIImageEditService');
            let maskBuffer;
            try {
                if (!maskBase64 || !maskBase64.startsWith('data:image/png;base64,')) {
                    throw new Error('蒙版必须是PNG格式的Base64数据');
                }
                const maskData = maskBase64.split(',')[1];
                maskBuffer = Buffer.from(maskData, 'base64');
                const sizeInMB = maskBuffer.length / (1024 * 1024);
                if (sizeInMB > 25) {
                    throw new Error(`蒙版大小超过25MB限制 (${sizeInMB.toFixed(2)}MB)`);
                }
                const maskFileName = `mask-${crypto.randomBytes(8).toString('hex')}.png`;
                maskPath = path.join(tempDir, maskFileName);
                fs.writeFileSync(maskPath, maskBuffer);
                common_1.Logger.log(`蒙版已保存到临时文件: ${maskPath}`, 'OpenAIImageEditService');
            }
            catch (error) {
                common_1.Logger.error(`处理蒙版图像失败: ${error.message}`, error, 'OpenAIImageEditService');
                throw new Error(`处理蒙版图像失败: ${error.message}`);
            }
            const formData = new FormData();
            formData.append('image', fs.createReadStream(imagePath));
            formData.append('mask', fs.createReadStream(maskPath));
            formData.append('prompt', prompt);
            formData.append('model', model);
            formData.append('size', size);
            formData.append('response_format', responseFormat);
            if (quality) {
                formData.append('quality', quality);
            }
            const options = {
                method: 'POST',
                url: `${proxyUrl}/v1/images/edits`,
                timeout: timeout,
                headers: Object.assign(Object.assign({}, formData.getHeaders()), { Authorization: `Bearer ${apiKey}` }),
                data: formData,
            };
            common_1.Logger.log(`正在准备发送请求到 ${options.url}，prompt: ${prompt}, model: ${model}, size: ${size}`, 'OpenAIImageEditService');
            if (assistantLogId) {
                await this.chatLogService.updateChatLog(assistantLogId, {
                    answer: '正在编辑图像...',
                    progress: '30%',
                    status: 2,
                });
            }
            const response = await (0, axios_1.default)(options);
            if (assistantLogId) {
                await this.chatLogService.updateChatLog(assistantLogId, {
                    answer: '图像编辑完成，正在处理结果...',
                    progress: '70%',
                    status: 2,
                });
            }
            const images = response.data.data;
            const imageUrls = [];
            for (let i = 0; i < images.length; i++) {
                const image = images[i];
                let imageUrl;
                if (responseFormat === 'b64_json') {
                    try {
                        const now = new Date();
                        const year = now.getFullYear();
                        const month = String(now.getMonth() + 1).padStart(2, '0');
                        const day = String(now.getDate()).padStart(2, '0');
                        const currentDate = `${year}${month}/${day}`;
                        const buffer = Buffer.from(image.b64_json, 'base64');
                        imageUrl = await this.uploadService.uploadBuffer({
                            buffer,
                            filename: `openai_edit_${Date.now()}_${i}.png`,
                            dir: `images/openai/${currentDate}`,
                        });
                    }
                    catch (error) {
                        common_1.Logger.error(`上传图像失败: ${error.message}`, error, 'OpenAIImageEditService');
                        throw new Error(`上传图像失败: ${error.message}`);
                    }
                }
                else {
                    try {
                        const now = new Date();
                        const year = now.getFullYear();
                        const month = String(now.getMonth() + 1).padStart(2, '0');
                        const day = String(now.getDate()).padStart(2, '0');
                        const currentDate = `${year}${month}/${day}`;
                        imageUrl = await this.uploadService.uploadFileFromUrl({
                            url: image.url,
                            dir: `images/openai/${currentDate}`,
                        });
                    }
                    catch (error) {
                        common_1.Logger.error(`上传图像失败: ${error.message}`, error, 'OpenAIImageEditService');
                        imageUrl = image.url;
                    }
                }
                imageUrls.push(imageUrl);
            }
            try {
                fs.unlinkSync(imagePath);
                fs.unlinkSync(maskPath);
            }
            catch (error) {
                common_1.Logger.warn(`清理临时文件失败: ${error.message}`, 'OpenAIImageEditService');
            }
            let replyText;
            try {
                replyText = await this.openAIChatService.chatFree(`我已经根据提示词"${prompt}"编辑了图像。请用中文回复，告诉用户图像已经编辑完成。`);
            }
            catch (error) {
                replyText = `已根据您的提示"${prompt}"编辑图像`;
                common_1.Logger.error('生成回复文本失败: ', error);
            }
            result.answer = replyText;
            result.fileInfo = imageUrls[0];
            result.status = 3;
            result.customId = JSON.stringify(imageUrls);
            result.progress = '100%';
            if (assistantLogId) {
                await this.chatLogService.updateChatLog(assistantLogId, {
                    fileInfo: result.fileInfo,
                    answer: result.answer,
                    progress: result.progress,
                    status: result.status,
                    customId: result.customId,
                });
            }
            if (onSuccess) {
                onSuccess(result);
            }
            return result;
        }
        catch (error) {
            result.status = 5;
            result.progress = '0%';
            const status = ((_a = error === null || error === void 0 ? void 0 : error.response) === null || _a === void 0 ? void 0 : _a.status) || 500;
            const message = ((_d = (_c = (_b = error === null || error === void 0 ? void 0 : error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.error) === null || _d === void 0 ? void 0 : _d.message) || error.message || '未知错误';
            common_1.Logger.error(`图像编辑失败: ${status} - ${message}`, error, 'OpenAIImageEditService');
            if (status === 429) {
                result.answer = '当前请求已过载，请稍后再试！';
            }
            else if (status === 400) {
                if (message.includes('Billing hard limit has been reached')) {
                    result.answer = '当前模型key已被封禁、已冻结当前调用Key、尝试重新对话试试吧！';
                }
                else if (message.includes('image and mask')) {
                    result.answer = '图像和蒙版尺寸不匹配，请确保它们具有相同的尺寸。';
                }
                else {
                    result.answer = `请求参数错误: ${message}`;
                }
            }
            else if (status === 401) {
                result.answer = 'API密钥无效或已过期';
            }
            else if (status === 500) {
                result.answer = '编辑图像失败，请检查你的提示词是否有非法描述！';
            }
            else {
                result.answer = `图像编辑失败: ${message}`;
            }
            if (assistantLogId) {
                await this.chatLogService.updateChatLog(assistantLogId, {
                    answer: result.answer,
                    status: result.status,
                });
            }
            if (onFailure) {
                onFailure(result);
            }
            return result;
        }
    }
};
OpenAIImageEditService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [globalConfig_service_1.GlobalConfigService,
        chatLog_service_1.ChatLogService,
        upload_service_1.UploadService,
        openaiChat_service_1.OpenAIChatService])
], OpenAIImageEditService);
exports.OpenAIImageEditService = OpenAIImageEditService;
